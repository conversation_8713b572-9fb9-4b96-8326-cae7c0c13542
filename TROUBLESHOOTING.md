# 🔧 TROUBLESHOOTING GUIDE - Legal Rebuttal Analysis System

## ❌ CURRENT ISSUES IDENTIFIED

Based on your error messages, here are the main problems and solutions:

---

## 🚨 ISSUE 1: Missing Dependencies

**Error:** `ModuleNotFoundError: No module named 'yaml'`

**Solution:**
```bash
# Option 1: Install essential packages only
python quick_fix.py

# Option 2: Install all dependencies
python install_dependencies.py

# Option 3: Manual installation
pip install pyyaml click python-docx PyPDF2 extract-msg openpyxl
```

---

## 🚨 ISSUE 2: Database Lock Error

**Error:** `[WinError 32] The process cannot access the file because it is being used by another process`

**Solution:**
```bash
# Delete the locked database file
del legal_analysis.db
del test_*.db

# Or restart your computer to release file locks
```

---

## 🚨 ISSUE 3: Missing setuptools

**Error:** `ModuleNotFoundError: No module named 'setuptools'`

**Solution:**
```bash
pip install setuptools
```

---

## ✅ STEP-BY-STEP FIX PROCESS

### Step 1: Install Essential Dependencies
```bash
python quick_fix.py
```

### Step 2: Test Simple System (No Dependencies)
```bash
python simple_analysis.py
```

### Step 3: If Step 2 Works, Install Full Dependencies
```bash
python install_dependencies.py
```

### Step 4: Test Full System
```bash
python main.py analyze
```

---

## 🎯 ALTERNATIVE: USE SIMPLE SYSTEM

If the full system continues to have issues, use the simplified version:

### Features of Simple System:
- ✅ **No complex dependencies** (only uses built-in Python)
- ✅ **Analyzes text files** (.txt, .log)
- ✅ **Detects age discrimination patterns**
- ✅ **Finds procedural violations**
- ✅ **Identifies supporting evidence**
- ✅ **Generates text reports**

### How to Use Simple System:
1. **Place your evidence files** in the correct directories
2. **Convert files to .txt format** if needed
3. **Run:** `python simple_analysis.py`

---

## 📁 FILE STRUCTURE REQUIREMENTS

Make sure this structure exists:
```
C:/Users/<USER>/OneDrive/Documents/Work/Investigation/
├── Evidence Against Me/           (Your evidence files)
│   ├── email1.txt                (Convert .msg to .txt)
│   ├── document1.txt             (Convert .pdf to .txt)
│   └── report1.txt               (Convert .docx to .txt)
├── References/                   (Supporting evidence)
│   ├── feedback1.txt
│   ├── commendation1.txt
│   └── review1.txt
└── Generated Output/             (Reports will be saved here)
```

---

## 🔄 FILE CONVERSION TIPS

### Convert MSG files to TXT:
1. Open the .msg file in Outlook
2. Copy the content
3. Paste into a .txt file

### Convert PDF files to TXT:
1. Open the PDF
2. Select all text (Ctrl+A)
3. Copy and paste into a .txt file

### Convert DOCX files to TXT:
1. Open the Word document
2. Save As → Plain Text (.txt)

---

## 🧪 TESTING CHECKLIST

Run these commands in order:

```bash
# 1. Test Python basics
python --version

# 2. Test simple system
python simple_analysis.py

# 3. Install essential dependencies
python quick_fix.py

# 4. Test with dependencies
python main.py --help

# 5. Run full analysis
python main.py analyze
```

---

## 🆘 IF NOTHING WORKS

### Last Resort Options:

1. **Use Simple System Only:**
   ```bash
   python simple_analysis.py
   ```

2. **Manual Analysis:**
   - Open your evidence files
   - Search for age-related terms: "56", "retire", "too old", "younger"
   - Search for bias terms: "predetermined", "already decided"
   - Document findings manually

3. **Contact Support:**
   - Check `analysis.log` for detailed error messages
   - Run `python --version` to confirm Python installation
   - List installed packages: `pip list`

---

## 📊 EXPECTED OUTPUTS

### If Working Correctly:
- ✅ Analysis report in `Generated Output/`
- ✅ Findings categorized by type
- ✅ Confidence scores for each finding
- ✅ Context around detected patterns

### Sample Output:
```
🚨 AGE DISCRIMINATION FINDINGS:
1. Pattern: '56'
   Context: At 56, you might want to consider retirement...
   Confidence: 80%

⚖️ PROCEDURAL VIOLATION FINDINGS:
1. Pattern: 'predetermined'
   Context: The investigation is predetermined...
   Confidence: 80%
```

---

## 🎯 SUCCESS CRITERIA

The system is working if:
- ✅ No import errors when running
- ✅ Finds your evidence directories
- ✅ Processes files without errors
- ✅ Generates analysis report
- ✅ Saves report to Generated Output folder

**Try the simple system first - it should work with minimal setup!**
