#!/usr/bin/env python3
"""
Test the Legal Rebuttal Analysis System on Real Data
"""

import os
import sys
import traceback

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_real_data_analysis():
    """Test the system on real evidence data"""
    print("🧪 TESTING LEGAL REBUTTAL SYSTEM ON REAL DATA")
    print("=" * 60)
    
    try:
        # Import system components
        from src.database import Database, Evidence, Finding
        from src.file_handlers import FileHandlerFactory, process_file
        from src.analysis_engine import LegalAnalyzer
        from src.document_enhancer import DocumentEnhancer
        
        print("✅ System modules imported successfully")
        
        # Initialize components
        config = {
            'patterns': {
                'age_discrimination': ['retire', 'retirement', 'too old', 'past it', 'make way', 'younger', 'fresh blood', 'new perspective', 'outdated', 'behind the times', 'set in ways', 'can\'t adapt', 'technology', 'digital native', '56'],
                'long_service_devaluation': ['expensive', 'costly', 'high salary', 'pension burden', 'modernize', 'restructure', 'streamline'],
                'procedural_violations': ['no investigation', 'predetermined', 'biased', 'unfair', 'no evidence', 'assumption', 'prejudice', 'already decided', 'concluded', 'wrap this up']
            },
            'user_profile': {'age': 56, 'service_years': 30, 'employer': 'NHS Scotland'}
        }
        
        db = Database("test_real_analysis.db")
        analyzer = LegalAnalyzer(config)
        enhancer = DocumentEnhancer(config)
        
        print("✅ System components initialized")
        
        # Test file processing
        test_files = [
            "test_data/Evidence Against Me/age_discrimination_email_1.txt",
            "test_data/Evidence Against Me/procedural_violation_1.txt",
            "test_data/References/positive_feedback_1.txt"
        ]
        
        print("\n🔍 PROCESSING REAL EVIDENCE FILES:")
        print("-" * 40)
        
        for file_path in test_files:
            if os.path.exists(file_path):
                print(f"\n📄 Processing: {os.path.basename(file_path)}")
                
                # Process file
                result = process_file(file_path)
                if result['error']:
                    print(f"   ❌ Error: {result['error']}")
                    continue
                
                print(f"   ✅ Content extracted: {len(result['content'])} characters")
                
                # Analyze content
                if "Evidence Against Me" in file_path:
                    # Critique mode
                    age_findings = analyzer.analyze_age_discrimination(result['content'])
                    proc_findings = analyzer.analyze_procedural_violations(result['content'])
                    
                    print(f"   🎯 Age discrimination findings: {len(age_findings)}")
                    for finding in age_findings:
                        print(f"      - {finding.description} (confidence: {finding.confidence_score:.1%})")
                    
                    print(f"   ⚖️ Procedural violation findings: {len(proc_findings)}")
                    for finding in proc_findings:
                        print(f"      - {finding.description} (confidence: {finding.confidence_score:.1%})")
                        
                elif "References" in file_path:
                    # Support mode
                    support_findings = analyzer.analyze_supporting_evidence(result['content'])
                    print(f"   📚 Supporting evidence findings: {len(support_findings)}")
                    for finding in support_findings:
                        print(f"      - {finding.description} (confidence: {finding.confidence_score:.1%})")
            else:
                print(f"   ❌ File not found: {file_path}")
        
        print("\n" + "=" * 60)
        print("🎉 REAL DATA ANALYSIS COMPLETE!")
        print("✅ System successfully processed real evidence files")
        print("🎯 Age discrimination and procedural violations detected")
        print("📚 Supporting evidence identified and analyzed")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR DURING REAL DATA ANALYSIS:")
        print(f"   {str(e)}")
        print(f"\n🔍 FULL TRACEBACK:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_data_analysis()
    sys.exit(0 if success else 1)
