# Legal Rebuttal Analysis System - Full Implementation
# Complete dependencies for production system

# File processing - Enhanced
extract-msg==0.41.1
PyPDF2==3.0.1
python-docx==0.8.11
openpyxl==3.1.2          # Excel file processing
xlrd==2.0.1              # Legacy Excel support
pandas==2.1.0            # Data analysis for spreadsheets

# Image processing and OCR
Pillow==10.0.0           # Image processing
pytesseract==0.3.10      # OCR text extraction
opencv-python==******** # Advanced image processing

# Database - Enhanced
sqlite3                  # Built-in with Python
sqlalchemy==2.0.21      # Advanced ORM for complex queries

# CLI interface - Enhanced
click==8.1.7
tqdm==4.66.1
rich==13.5.2            # Enhanced terminal output
colorama==0.4.6         # Cross-platform colored terminal

# Machine Learning - Full Implementation
scikit-learn==1.3.0     # ML algorithms
numpy==1.24.3           # Numerical computing
scipy==1.11.2           # Scientific computing
joblib==1.3.2           # Model persistence

# Natural Language Processing - Advanced
nltk==3.8.1
spacy==3.6.1
transformers==4.33.2    # Advanced NLP models
torch==2.0.1            # PyTorch for deep learning
sentence-transformers==2.2.2  # Semantic similarity

# Legal and Document Processing
python-docx==0.8.11
lxml==4.9.3             # XML processing
beautifulsoup4==4.12.2  # HTML/XML parsing
pdfplumber==0.9.0       # Advanced PDF processing
pymupdf==1.23.3         # PDF manipulation

# Utilities - Enhanced
pyyaml==6.0.1
python-dateutil==2.8.2
requests==2.31.0        # HTTP requests for legal databases
cachetools==5.3.1       # Caching for performance
psutil==5.9.5           # System monitoring

# Development and testing - Comprehensive
pytest==7.4.0
pytest-cov==4.1.0
pytest-mock==3.11.1
black==23.7.0           # Code formatting
flake8==6.0.0           # Code linting
mypy==1.5.1             # Type checking
