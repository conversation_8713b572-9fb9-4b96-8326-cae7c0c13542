#!/usr/bin/env python3
"""
BASIC Legal Analysis - NO EXTERNAL DEPENDENCIES
This version uses ONLY built-in Python libraries
"""

import os
import re
from datetime import datetime

def analyze_file_for_patterns(file_path):
    """Analyze a single file for legal patterns"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except:
        return []
    
    findings = []
    content_lower = content.lower()
    
    # Age discrimination patterns
    age_patterns = [
        'retire', 'retirement', 'too old', '56', 'younger', 'fresh blood',
        'outdated', 'behind the times', 'set in ways', 'technology'
    ]
    
    # Procedural violation patterns  
    proc_patterns = [
        'predetermined', 'already decided', 'biased', 'unfair',
        'no investigation', 'concluded', 'wrap this up'
    ]
    
    # Positive evidence patterns
    positive_patterns = [
        'excellent', 'outstanding', 'commend', 'praise', 'valuable',
        'asset', 'experience', 'mentor', 'dedicated'
    ]
    
    # Check for age discrimination
    for pattern in age_patterns:
        if pattern in content_lower:
            findings.append({
                'file': os.path.basename(file_path),
                'type': 'AGE_DISCRIMINATION',
                'pattern': pattern,
                'file_path': file_path
            })
    
    # Check for procedural violations
    for pattern in proc_patterns:
        if pattern in content_lower:
            findings.append({
                'file': os.path.basename(file_path),
                'type': 'PROCEDURAL_VIOLATION', 
                'pattern': pattern,
                'file_path': file_path
            })
    
    # Check for positive evidence
    for pattern in positive_patterns:
        if pattern in content_lower:
            findings.append({
                'file': os.path.basename(file_path),
                'type': 'POSITIVE_EVIDENCE',
                'pattern': pattern,
                'file_path': file_path
            })
    
    return findings

def scan_directory(directory_path):
    """Scan a directory for files and analyze them"""
    if not os.path.exists(directory_path):
        print(f"Directory not found: {directory_path}")
        return []
    
    all_findings = []
    file_count = 0
    
    for root, dirs, files in os.walk(directory_path):
        for file in files:
            file_path = os.path.join(root, file)
            print(f"Checking: {file}")
            
            findings = analyze_file_for_patterns(file_path)
            all_findings.extend(findings)
            
            if findings:
                file_count += 1
                print(f"  Found {len(findings)} patterns in {file}")
    
    print(f"Scanned directory: {directory_path}")
    print(f"Files with findings: {file_count}")
    print(f"Total findings: {len(all_findings)}")
    
    return all_findings

def generate_simple_report(all_findings):
    """Generate a basic text report"""
    report = []
    report.append("=" * 60)
    report.append("BASIC LEGAL ANALYSIS REPORT")
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("=" * 60)
    
    # Group by type
    age_findings = [f for f in all_findings if f['type'] == 'AGE_DISCRIMINATION']
    proc_findings = [f for f in all_findings if f['type'] == 'PROCEDURAL_VIOLATION']
    pos_findings = [f for f in all_findings if f['type'] == 'POSITIVE_EVIDENCE']
    
    if age_findings:
        report.append(f"\nAGE DISCRIMINATION FINDINGS ({len(age_findings)}):")
        report.append("-" * 40)
        for finding in age_findings:
            report.append(f"File: {finding['file']}")
            report.append(f"Pattern: {finding['pattern']}")
            report.append("")
    
    if proc_findings:
        report.append(f"\nPROCEDURAL VIOLATIONS ({len(proc_findings)}):")
        report.append("-" * 40)
        for finding in proc_findings:
            report.append(f"File: {finding['file']}")
            report.append(f"Pattern: {finding['pattern']}")
            report.append("")
    
    if pos_findings:
        report.append(f"\nPOSITIVE EVIDENCE ({len(pos_findings)}):")
        report.append("-" * 40)
        for finding in pos_findings:
            report.append(f"File: {finding['file']}")
            report.append(f"Pattern: {finding['pattern']}")
            report.append("")
    
    report.append(f"\nSUMMARY:")
    report.append(f"Age Discrimination: {len(age_findings)}")
    report.append(f"Procedural Violations: {len(proc_findings)}")
    report.append(f"Positive Evidence: {len(pos_findings)}")
    report.append(f"Total Findings: {len(all_findings)}")
    
    return "\n".join(report)

def main():
    """Main function - NO DEPENDENCIES REQUIRED"""
    print("BASIC LEGAL ANALYZER - NO EXTERNAL DEPENDENCIES")
    print("=" * 60)
    
    # Define paths
    base_path = "C:/Users/<USER>/OneDrive/Documents/Work/Investigation"
    evidence_path = os.path.join(base_path, "Evidence Against Me")
    references_path = os.path.join(base_path, "References")
    output_path = os.path.join(base_path, "Generated Output")
    
    print(f"Looking for evidence in: {evidence_path}")
    print(f"Looking for references in: {references_path}")
    
    # Scan directories
    all_findings = []
    
    if os.path.exists(evidence_path):
        print(f"\nScanning Evidence Against Me...")
        evidence_findings = scan_directory(evidence_path)
        all_findings.extend(evidence_findings)
    else:
        print(f"Evidence directory not found: {evidence_path}")
    
    if os.path.exists(references_path):
        print(f"\nScanning References...")
        ref_findings = scan_directory(references_path)
        all_findings.extend(ref_findings)
    else:
        print(f"References directory not found: {references_path}")
    
    # Generate report
    if all_findings:
        print(f"\nGenerating report...")
        report = generate_simple_report(all_findings)
        
        # Create output directory
        os.makedirs(output_path, exist_ok=True)
        
        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(output_path, f"basic_analysis_{timestamp}.txt")
        
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"Report saved: {report_file}")
        print("\n" + report)
    else:
        print("\nNo findings detected.")
        print("Make sure your evidence files are in the correct directories.")
    
    print("\nAnalysis complete.")

if __name__ == "__main__":
    main()
