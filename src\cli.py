"""
Command Line Interface for Legal Rebuttal Analysis System
24-Hour MVP Implementation
"""

import os
import sys
import yaml
import logging
import click
from datetime import datetime
from typing import List, Dict

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import Database, Evidence, Finding
from file_handlers import FileHandlerFactory, process_file
from analysis_engine import LegalAnalyzer
from document_enhancer import DocumentEnhancer
from synthetic_data import SyntheticDataGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analysis.log'),
        logging.StreamHandler()
    ]
)

class LegalRebuttalSystem:
    """Main system orchestrator"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
        self.db = Database(self.config.get('paths', {}).get('database', 'legal_analysis.db'))
        self.analyzer = LegalAnalyzer(self.config)
        self.enhancer = DocumentEnhancer(self.config)
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logging.error(f"Config file not found: {config_path}")
            return {}
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            return {}
    
    def analyze_evidence_folder(self, folder_path: str, analysis_mode: str) -> List[Dict]:
        """Analyze all files in an evidence folder"""
        if not os.path.exists(folder_path):
            logging.error(f"Folder not found: {folder_path}")
            return []
        
        all_findings = []
        supported_extensions = FileHandlerFactory.supported_extensions()
        
        logging.info(f"Analyzing folder: {folder_path} (mode: {analysis_mode})")
        
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in supported_extensions:
                    logging.info(f"Processing file: {file_path}")
                    
                    # Process file
                    file_result = process_file(file_path)
                    if file_result['error']:
                        logging.error(f"Error processing {file_path}: {file_result['error']}")
                        continue
                    
                    # Store evidence in database
                    evidence = Evidence(
                        id=None,
                        file_path=file_path,
                        file_type=file_ext,
                        content=file_result['content'],
                        analysis_mode=analysis_mode,
                        created_date=datetime.now()
                    )
                    evidence_id = self.db.store_evidence(evidence)
                    
                    # Analyze content
                    if analysis_mode == 'critique':
                        findings = self.analyzer.analyze_critique_mode(file_result['content'], file_path)
                    else:
                        findings = self.analyzer.analyze_support_mode(file_result['content'], file_path)
                    
                    # Store findings
                    for finding in findings:
                        db_finding = Finding(
                            id=None,
                            evidence_id=evidence_id,
                            finding_type=finding.finding_type,
                            description=finding.description,
                            confidence_score=finding.confidence_score,
                            created_date=datetime.now()
                        )
                        self.db.store_finding(db_finding)
                        
                        # Add to results
                        all_findings.append({
                            'finding_type': finding.finding_type,
                            'description': finding.description,
                            'confidence_score': finding.confidence_score,
                            'evidence_text': finding.evidence_text,
                            'recommendation': finding.recommendation,
                            'file_path': file_path
                        })
                else:
                    logging.info(f"Skipping unsupported file: {file_path}")
        
        logging.info(f"Analysis complete. Found {len(all_findings)} findings.")
        return all_findings
    
    def run_full_analysis(self) -> Dict:
        """Run complete analysis workflow"""
        logging.info("Starting full legal analysis...")
        
        # Clear previous analysis
        self.db.clear_all_data()
        
        paths = self.config.get('paths', {})
        evidence_against_path = paths.get('evidence_against', 'Evidence Against Me/')
        references_path = paths.get('references', 'References/')
        
        results = {
            'critique_findings': [],
            'support_findings': [],
            'total_findings': 0,
            'enhanced_document': None
        }
        
        # Analyze "Evidence Against Me" folder (critique mode)
        if os.path.exists(evidence_against_path):
            critique_findings = self.analyze_evidence_folder(evidence_against_path, 'critique')
            results['critique_findings'] = critique_findings
        else:
            logging.warning(f"Evidence Against Me folder not found: {evidence_against_path}")
        
        # Analyze "References" folder (support mode)
        if os.path.exists(references_path):
            support_findings = self.analyze_evidence_folder(references_path, 'support')
            results['support_findings'] = support_findings
        else:
            logging.warning(f"References folder not found: {references_path}")
        
        # Combine all findings
        all_findings = results['critique_findings'] + results['support_findings']
        results['total_findings'] = len(all_findings)
        
        # Enhance rebuttal document
        rebuttal_path = paths.get('rebuttal_document', "Wayne Gault's Response v1.docx")
        if os.path.exists(rebuttal_path):
            enhanced_doc = self.enhancer.enhance_document(rebuttal_path, all_findings)
            results['enhanced_document'] = enhanced_doc
        else:
            logging.warning(f"Rebuttal document not found: {rebuttal_path}")
        
        return results

@click.group()
def cli():
    """Legal Rebuttal Analysis System - 24 Hour MVP"""
    pass

@cli.command()
@click.option('--output-dir', default='test_data', help='Output directory for test data')
def generate_test_data(output_dir):
    """Generate synthetic test data for system testing"""
    click.echo("🔧 Generating synthetic test data...")
    
    generator = SyntheticDataGenerator(output_dir)
    generator.create_test_files()
    
    click.echo(f"✅ Test data generated in {output_dir}/")
    click.echo("📁 Use this data to test the system before analyzing real evidence")

@cli.command()
@click.option('--config', default='config.yaml', help='Configuration file path')
def analyze(config):
    """Run complete legal analysis on evidence folders"""
    click.echo("🚀 Starting Legal Rebuttal Analysis...")
    click.echo(f"⚖️  Analyzing evidence for Wayne Gault (Age 56, 30+ years NHS service)")
    
    system = LegalRebuttalSystem(config)
    
    with click.progressbar(length=100, label='Analyzing evidence') as bar:
        # Simulate progress for user feedback
        bar.update(20)
        results = system.run_full_analysis()
        bar.update(80)
    
    # Display results
    click.echo("\n" + "="*60)
    click.echo("📊 ANALYSIS RESULTS")
    click.echo("="*60)
    
    critique_count = len(results['critique_findings'])
    support_count = len(results['support_findings'])
    total_count = results['total_findings']
    
    click.echo(f"🔍 Evidence Against Me: {critique_count} findings")
    click.echo(f"📚 References (Support): {support_count} findings")
    click.echo(f"📈 Total Findings: {total_count}")
    
    if results['enhanced_document']:
        click.echo(f"📝 Enhanced Document: {results['enhanced_document']}")
    
    # Show top findings
    if critique_count > 0:
        click.echo("\n🚨 TOP CRITIQUE FINDINGS:")
        for i, finding in enumerate(results['critique_findings'][:3], 1):
            click.echo(f"  {i}. {finding['finding_type']}: {finding['description'][:80]}...")
            click.echo(f"     Confidence: {finding['confidence_score']:.1%}")
    
    if support_count > 0:
        click.echo("\n✅ TOP SUPPORTING EVIDENCE:")
        for i, finding in enumerate(results['support_findings'][:3], 1):
            click.echo(f"  {i}. {finding['finding_type']}: {finding['description'][:80]}...")
            click.echo(f"     Confidence: {finding['confidence_score']:.1%}")
    
    click.echo("\n✅ Analysis complete! Check the enhanced document for detailed recommendations.")

@cli.command()
def status():
    """Check system status and configuration"""
    click.echo("🔧 System Status Check")
    click.echo("-" * 40)
    
    # Check configuration
    if os.path.exists('config.yaml'):
        click.echo("✅ Configuration file found")
    else:
        click.echo("❌ Configuration file missing")
    
    # Check required directories
    required_dirs = ['Evidence Against Me', 'References', 'Generated Output']
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            file_count = len([f for f in os.listdir(dir_name) if os.path.isfile(os.path.join(dir_name, f))])
            click.echo(f"✅ {dir_name}: {file_count} files")
        else:
            click.echo(f"❌ {dir_name}: Directory not found")
    
    # Check supported file types
    supported = FileHandlerFactory.supported_extensions()
    click.echo(f"📄 Supported file types: {', '.join(supported)}")
    
    # Check database
    try:
        db = Database()
        click.echo("✅ Database connection working")
    except Exception as e:
        click.echo(f"❌ Database error: {e}")

@cli.command()
def help_wayne():
    """Quick help guide for Wayne"""
    click.echo("🆘 QUICK START GUIDE FOR WAYNE")
    click.echo("=" * 50)
    click.echo()
    click.echo("1. FIRST TIME SETUP:")
    click.echo("   python -m src.cli generate-test-data")
    click.echo("   (This creates test data to verify the system works)")
    click.echo()
    click.echo("2. CHECK SYSTEM STATUS:")
    click.echo("   python -m src.cli status")
    click.echo("   (Verify your folders and files are set up correctly)")
    click.echo()
    click.echo("3. RUN ANALYSIS:")
    click.echo("   python -m src.cli analyze")
    click.echo("   (This analyzes your evidence and creates enhanced document)")
    click.echo()
    click.echo("4. FOLDER STRUCTURE NEEDED:")
    click.echo("   📁 Evidence Against Me/  (files to critique)")
    click.echo("   📁 References/           (files supporting your case)")
    click.echo("   📄 Wayne Gault's Response v1.docx  (your rebuttal)")
    click.echo()
    click.echo("5. RESULTS:")
    click.echo("   📁 Generated Output/     (enhanced documents appear here)")
    click.echo("   📄 analysis.log          (detailed log of what happened)")
    click.echo()
    click.echo("🎯 The system focuses on:")
    click.echo("   • Age discrimination (you're 56)")
    click.echo("   • Long service rights (30+ years)")
    click.echo("   • Procedural violations")
    click.echo("   • Supporting evidence for your character")

if __name__ == '__main__':
    cli()
