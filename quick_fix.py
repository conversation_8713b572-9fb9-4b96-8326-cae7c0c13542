#!/usr/bin/env python3
"""
Quick Fix for Legal Rebuttal Analysis System
Install essential dependencies and test the system
"""

import subprocess
import sys
import os

def install_essential():
    """Install only the most essential packages"""
    essential = ["pyyaml", "click"]
    
    print("🔧 Installing essential dependencies...")
    
    for package in essential:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed")
        except:
            print(f"❌ Failed to install {package}")
    
    print("✅ Essential dependencies installed")

def test_simple_system():
    """Test the simple analysis system"""
    print("\n🧪 Testing simple analysis system...")
    
    try:
        # Test if we can run the simple analysis
        result = subprocess.run([sys.executable, "simple_analysis.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Simple analysis system works!")
            print("Output:")
            print(result.stdout)
        else:
            print("❌ Simple analysis system failed:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ Test timed out - system may be working but slow")
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    print("🚀 QUICK FIX FOR LEGAL REBUTTAL ANALYSIS SYSTEM")
    print("=" * 60)
    
    # Install essential dependencies
    install_essential()
    
    # Test the simple system
    test_simple_system()
    
    print("\n📋 NEXT STEPS:")
    print("1. Run: python simple_analysis.py")
    print("2. If that works, try: python install_dependencies.py")
    print("3. Then try: python main.py analyze")
    
    print("\n✅ Quick fix complete!")

if __name__ == "__main__":
    main()
