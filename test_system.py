#!/usr/bin/env python3
"""
Quick system test for Legal Rebuttal Analysis System
Verifies all components work together
"""

import os
import sys
import tempfile
import shutil
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_system():
    """Run complete system test"""
    print("🧪 Testing Legal Rebuttal Analysis System...")
    print("=" * 50)
    
    # Test 1: Import all modules
    print("1. Testing module imports...")
    try:
        from src.database import Database, Evidence, Finding
        from src.file_handlers import FileHandlerFactory, process_file
        from src.analysis_engine import LegalAnalyzer
        from src.document_enhancer import DocumentEnhancer
        from src.synthetic_data import SyntheticDataGenerator
        print("   ✅ All modules imported successfully")
    except Exception as e:
        print(f"   ❌ Import error: {e}")
        return False
    
    # Test 2: Database operations
    print("2. Testing database operations...")
    try:
        # Use a temporary file database for testing
        import tempfile
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        db = Database(temp_db.name)
        
        # Test evidence storage
        evidence = Evidence(
            id=None,
            file_path="test.txt",
            file_type=".txt",
            content="Test content with age discrimination: Wayne is 56 and should retire.",
            analysis_mode="critique",
            created_date=datetime.now()
        )
        evidence_id = db.store_evidence(evidence)
        
        # Test finding storage
        finding = Finding(
            id=None,
            evidence_id=evidence_id,
            finding_type="age_discrimination",
            description="Age discrimination detected",
            confidence_score=0.8,
            created_date=datetime.now()
        )
        finding_id = db.store_finding(finding)
        
        # Test retrieval
        findings = db.get_all_findings()
        assert len(findings) == 1
        assert findings[0]['finding_type'] == 'age_discrimination'

        # Cleanup
        os.unlink(temp_db.name)

        print("   ✅ Database operations working")
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False
    
    # Test 3: File processing
    print("3. Testing file processing...")
    try:
        # Create test file
        test_content = "Wayne is 56 years old and management thinks he should retire. This is age discrimination."
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            test_file = f.name
        
        # Test file processing
        result = process_file(test_file)
        assert result['error'] is None
        assert 'Wayne is 56' in result['content']
        
        # Cleanup
        os.unlink(test_file)
        
        print("   ✅ File processing working")
    except Exception as e:
        print(f"   ❌ File processing error: {e}")
        return False
    
    # Test 4: Legal analysis
    print("4. Testing legal analysis...")
    try:
        config = {
            'patterns': {
                'age_discrimination': ['retire', 'too old', '56'],
                'long_service_devaluation': ['expensive', 'costly'],
                'procedural_violations': ['predetermined', 'biased']
            },
            'user_profile': {'age': 56, 'service_years': 30}
        }
        
        analyzer = LegalAnalyzer(config)
        test_text = "Wayne is 56 and should retire. He's too old for this modern role."
        
        results = analyzer.analyze_critique_mode(test_text, "test.txt")
        assert len(results) > 0
        assert any(r.finding_type == 'age_discrimination' for r in results)
        
        print("   ✅ Legal analysis working")
    except Exception as e:
        print(f"   ❌ Legal analysis error: {e}")
        return False
    
    # Test 5: Document enhancement
    print("5. Testing document enhancement...")
    try:
        enhancer = DocumentEnhancer({})
        
        # Create test document
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Wayne Gault's Response\n\nThis is a test rebuttal document.")
            test_doc = f.name
        
        # Create output directory
        os.makedirs("Generated Output", exist_ok=True)
        
        # Test enhancement
        test_findings = [
            {
                'finding_type': 'age_discrimination',
                'description': 'Age discrimination detected in evidence',
                'confidence_score': 0.8,
                'file_path': 'test.txt',
                'evidence_text': 'Wayne is 56 and should retire',
                'recommendation': 'Challenge this as age discrimination'
            }
        ]
        
        enhanced_doc = enhancer.enhance_document(test_doc, test_findings)
        assert enhanced_doc is not None
        assert os.path.exists(enhanced_doc)
        
        # Cleanup
        os.unlink(test_doc)
        if os.path.exists(enhanced_doc):
            os.unlink(enhanced_doc)
        
        print("   ✅ Document enhancement working")
    except Exception as e:
        print(f"   ❌ Document enhancement error: {e}")
        return False
    
    # Test 6: Synthetic data generation
    print("6. Testing synthetic data generation...")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            generator = SyntheticDataGenerator(temp_dir)
            generator.create_test_files()
            
            # Check files were created
            evidence_dir = os.path.join(temp_dir, "Evidence Against Me")
            references_dir = os.path.join(temp_dir, "References")
            
            assert os.path.exists(evidence_dir)
            assert os.path.exists(references_dir)
            assert len(os.listdir(evidence_dir)) > 0
            assert len(os.listdir(references_dir)) > 0
        
        print("   ✅ Synthetic data generation working")
    except Exception as e:
        print(f"   ❌ Synthetic data error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED!")
    print("✅ System is ready for production use")
    print("🚀 Wayne can now analyze his evidence files")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = test_system()
    sys.exit(0 if success else 1)
