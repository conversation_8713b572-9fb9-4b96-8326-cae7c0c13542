# Legal Rebuttal Analysis System - 24 Hour MVP

🚨 **URGENT DELIVERY** - Production-ready system for <PERSON>'s NHS Scotland employment case

## 🎯 Mission
AI-powered analysis system to strengthen Wayne's legal defense by:
- Detecting age discrimination patterns (<PERSON> is 56)
- Leveraging long service rights (30+ years NHS)
- Finding procedural violations
- Enhancing rebuttal document with strategic recommendations

## ⚡ Quick Start (5 Minutes)

### 1. Install Dependencies
```bash
# Create virtual environment
python -m venv legal_env
legal_env\Scripts\activate  # Windows
# source legal_env/bin/activate  # Linux/Mac

# Install requirements
pip install -r requirements.txt
```

### 2. Test System with Synthetic Data
```bash
# Generate test data
python main.py generate-test-data

# Run analysis on test data
cd test_data
python ../main.py analyze
```

### 3. Set Up Your Real Evidence
Create this folder structure:
```
📁 Evidence Against Me/     (files to critique - emails, reports showing bias)
📁 References/              (files supporting your case - positive feedback, etc.)
📄 <PERSON>'s Response v1.docx  (your current rebuttal document)
```

### 4. Run Analysis on Your Evidence
```bash
python main.py analyze
```

### 5. Check Results
- Enhanced document appears in `Generated Output/`
- Detailed log in `analysis.log`

## 🔧 System Commands

```bash
python main.py help-wayne     # Complete guide for Wayne
python main.py status         # Check system setup
python main.py analyze        # Run full analysis
python main.py generate-test-data  # Create test files
```

## 📊 What the System Detects

### 🚨 Age Discrimination (Primary Protection)
- Direct age bias language ("too old", "retire", "past it")
- Retirement pressure and stereotyping
- Technology assumptions about older employees
- Cost-based targeting due to salary/pension

### ⚖️ Procedural Violations
- Predetermined decisions
- Biased investigations
- Failure to follow ACAS Code
- Natural justice breaches

### 💪 Supporting Evidence
- Positive performance indicators
- Character evidence
- Long service value
- Institutional knowledge contributions

## 🎯 Wayne-Specific Analysis

The system is calibrated for Wayne's situation:
- **Age 56** - Enhanced age discrimination protection
- **30+ Years NHS Service** - Long service rights and institutional knowledge
- **NHS Scotland** - Public sector equality duties and staff governance standards
- **Senior Manager** - Leadership experience and mentoring value

## 📁 File Types Supported

- ✅ **MSG files** (Outlook emails) - Primary evidence format
- ✅ **PDF files** (Reports, statements, documents)
- ✅ **DOCX files** (Word documents, letters)
- ✅ **TXT files** (Plain text documents)

## 🚀 Output

The system generates:
1. **Enhanced Rebuttal Document** with strategic recommendations
2. **Analysis Summary** showing all findings by type
3. **Detailed Recommendations** for each piece of evidence
4. **Strategic Legal Advice** specific to Wayne's case

## ⚠️ Important Notes

- **Backup Created**: Original documents are automatically backed up
- **Local Processing**: All analysis happens on your computer (no cloud/internet)
- **Confidence Scores**: Each finding has a confidence percentage
- **Evidence References**: All recommendations link back to source files

## 🆘 Troubleshooting

### Common Issues:
1. **"No module named 'extract-msg'"** → Run `pip install -r requirements.txt`
2. **"Folder not found"** → Check folder structure matches exactly
3. **"No findings detected"** → Check file formats are supported
4. **"Permission denied"** → Close Word/Outlook before running analysis

### Get Help:
```bash
python main.py help-wayne    # Complete user guide
python main.py status        # System diagnostics
```

## 📞 Support

If you encounter issues:
1. Check `analysis.log` for detailed error messages
2. Run `python main.py status` to verify setup
3. Try with test data first: `python main.py generate-test-data`

## 🏁 Success Criteria

✅ **System Working** if you can:
- Generate and analyze test data successfully
- See findings in the enhanced document
- Get strategic recommendations for your case

✅ **Ready for Legal Use** when:
- Your evidence folders are analyzed without errors
- Enhanced document contains useful recommendations
- Age discrimination and procedural violations are detected

---

**🎯 Remember: This system is designed specifically for Wayne's case (Age 56, 30+ years NHS service). The analysis focuses on age discrimination, long service rights, and NHS Scotland employment protections.**
