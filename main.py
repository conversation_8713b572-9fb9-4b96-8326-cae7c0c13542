#!/usr/bin/env python3
"""
Legal Rebuttal Analysis System - Main Entry Point
24-Hour MVP for <PERSON>'s NHS Scotland Employment Case

Usage:
    python main.py --help
    python main.py generate-test-data
    python main.py analyze
    python main.py status
"""

import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.cli import cli

if __name__ == '__main__':
    print("🏛️  Legal Rebuttal Analysis System")
    print("⚖️  24-Hour MVP for <PERSON>'s Case")
    print("🎯 Focus: Age 56, 30+ Years NHS Service")
    print("-" * 50)
    
    cli()
