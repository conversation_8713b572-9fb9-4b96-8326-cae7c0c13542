"""
Document Enhancement Module for Legal Rebuttal Analysis System
Simple Word document comment insertion for 24-hour MVP
"""

import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict
from dataclasses import dataclass

@dataclass
class Enhancement:
    section: str
    content: str
    reasoning: str
    confidence: float
    evidence_file: str

class DocumentEnhancer:
    """Simple document enhancer for MVP - adds comments to text files"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.backup_dir = "backups"
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def create_backup(self, document_path: str) -> str:
        """Create backup of original document"""
        if not os.path.exists(document_path):
            logging.warning(f"Document not found: {document_path}")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_{timestamp}_{os.path.basename(document_path)}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        try:
            shutil.copy2(document_path, backup_path)
            logging.info(f"Backup created: {backup_path}")
            return backup_path
        except Exception as e:
            logging.error(f"Failed to create backup: {e}")
            return None
    
    def enhance_document(self, document_path: str, findings: List[Dict]) -> str:
        """Enhance document with analysis findings"""
        # Create backup first
        backup_path = self.create_backup(document_path)
        if not backup_path:
            logging.error("Cannot proceed without backup")
            return None
        
        try:
            # For MVP, we'll create an enhanced text version
            enhanced_path = self._create_enhanced_text_version(document_path, findings)
            logging.info(f"Enhanced document created: {enhanced_path}")
            return enhanced_path
        except Exception as e:
            logging.error(f"Failed to enhance document: {e}")
            return None
    
    def _create_enhanced_text_version(self, document_path: str, findings: List[Dict]) -> str:
        """Create enhanced text version with comments"""
        # Read original document
        try:
            with open(document_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
        except:
            # Try different encoding
            with open(document_path, 'r', encoding='latin-1') as f:
                original_content = f.read()
        
        # Create enhanced version
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        enhanced_filename = f"enhanced_{timestamp}_{os.path.basename(document_path)}"
        enhanced_path = os.path.join("Generated Output", enhanced_filename)
        
        # Ensure output directory exists
        os.makedirs("Generated Output", exist_ok=True)
        
        # Build enhanced content
        enhanced_content = self._build_enhanced_content(original_content, findings)
        
        with open(enhanced_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_content)
        
        return enhanced_path
    
    def _build_enhanced_content(self, original_content: str, findings: List[Dict]) -> str:
        """Build enhanced content with analysis results"""
        enhanced = []
        enhanced.append("=" * 80)
        enhanced.append("ENHANCED LEGAL REBUTTAL DOCUMENT")
        enhanced.append("Generated by Legal Analysis System")
        enhanced.append(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        enhanced.append("=" * 80)
        enhanced.append("")
        
        # Add analysis summary
        enhanced.append("ANALYSIS SUMMARY")
        enhanced.append("-" * 40)
        
        # Group findings by type
        findings_by_type = {}
        for finding in findings:
            finding_type = finding.get('finding_type', 'unknown')
            if finding_type not in findings_by_type:
                findings_by_type[finding_type] = []
            findings_by_type[finding_type].append(finding)
        
        # Add summary for each type
        for finding_type, type_findings in findings_by_type.items():
            enhanced.append(f"\\n{finding_type.upper().replace('_', ' ')}: {len(type_findings)} findings")
            
            for i, finding in enumerate(type_findings[:3], 1):  # Show top 3
                confidence = finding.get('confidence_score', 0)
                description = finding.get('description', 'No description')
                enhanced.append(f"  {i}. {description} (Confidence: {confidence:.1%})")
        
        enhanced.append("")
        enhanced.append("=" * 80)
        enhanced.append("ORIGINAL DOCUMENT WITH ENHANCEMENTS")
        enhanced.append("=" * 80)
        enhanced.append("")
        
        # Add original content
        enhanced.append(original_content)
        enhanced.append("")
        
        # Add detailed analysis section
        enhanced.append("=" * 80)
        enhanced.append("DETAILED ANALYSIS AND RECOMMENDATIONS")
        enhanced.append("=" * 80)
        enhanced.append("")
        
        # Add detailed findings
        for finding_type, type_findings in findings_by_type.items():
            enhanced.append(f"\\n{finding_type.upper().replace('_', ' ')} ANALYSIS")
            enhanced.append("-" * 50)
            
            for i, finding in enumerate(type_findings, 1):
                enhanced.append(f"\\nFinding #{i}:")
                enhanced.append(f"Description: {finding.get('description', 'N/A')}")
                enhanced.append(f"Confidence: {finding.get('confidence_score', 0):.1%}")
                enhanced.append(f"Source File: {finding.get('file_path', 'Unknown')}")
                
                # Add evidence text if available
                evidence_text = finding.get('evidence_text', '')
                if evidence_text:
                    enhanced.append(f"Evidence Text: {evidence_text[:200]}...")
                
                # Add recommendation
                recommendation = self._get_recommendation_for_finding(finding)
                enhanced.append(f"Recommendation: {recommendation}")
                enhanced.append("")
        
        # Add strategic recommendations
        enhanced.append("\\n" + "=" * 80)
        enhanced.append("STRATEGIC RECOMMENDATIONS FOR WAYNE'S CASE")
        enhanced.append("=" * 80)
        enhanced.append("")
        
        strategic_recommendations = self._generate_strategic_recommendations(findings_by_type)
        for rec in strategic_recommendations:
            enhanced.append(f"• {rec}")
        
        enhanced.append("")
        enhanced.append("=" * 80)
        enhanced.append("END OF ENHANCED DOCUMENT")
        enhanced.append("=" * 80)
        
        return "\\n".join(enhanced)
    
    def _get_recommendation_for_finding(self, finding: Dict) -> str:
        """Get specific recommendation for a finding"""
        finding_type = finding.get('finding_type', '')
        
        if finding_type == 'age_discrimination':
            return "Challenge this as direct age discrimination under Equality Act 2010. Request burden of proof shift to employer to justify treatment."
        elif finding_type == 'long_service_devaluation':
            return "Highlight Wayne's 30+ years of valuable service and institutional knowledge. Challenge cost-based targeting as indirect age discrimination."
        elif finding_type == 'procedural_violation':
            return "Cite breach of ACAS Code of Practice and natural justice principles. Request investigation be declared invalid."
        elif finding_type == 'positive_evidence':
            return "Use this evidence to demonstrate Wayne's value and counter negative allegations. Include in character evidence section."
        else:
            return "Review this finding and consider how it strengthens Wayne's defense position."
    
    def _generate_strategic_recommendations(self, findings_by_type: Dict) -> List[str]:
        """Generate high-level strategic recommendations"""
        recommendations = []
        
        if 'age_discrimination' in findings_by_type:
            count = len(findings_by_type['age_discrimination'])
            recommendations.append(f"STRONG AGE DISCRIMINATION CASE: {count} instances detected. This is Wayne's primary legal protection as a 56-year-old employee.")
        
        if 'long_service_devaluation' in findings_by_type:
            recommendations.append("LONG SERVICE RIGHTS: Emphasize Wayne's 30+ years of unblemished NHS service as enhanced protection against unfair treatment.")
        
        if 'procedural_violation' in findings_by_type:
            recommendations.append("PROCEDURAL FAILURES: Challenge the investigation process as fundamentally flawed and biased.")
        
        if 'positive_evidence' in findings_by_type:
            count = len(findings_by_type['positive_evidence'])
            recommendations.append(f"CHARACTER EVIDENCE: {count} pieces of positive evidence support Wayne's reputation and performance.")
        
        # Always add these strategic points for Wayne
        recommendations.extend([
            "PENSION PROXIMITY: At age 56, Wayne has enhanced protection due to proximity to retirement.",
            "NHS SCOTLAND POLICIES: Ensure all Staff Governance Standards and Public Sector Equality Duty obligations are highlighted.",
            "REMEDY CALCULATION: Age discrimination awards range £11,200-£56,200+ (Vento bands). Pension loss calculations critical.",
            "SETTLEMENT LEVERAGE: Strong discrimination case provides significant negotiating power."
        ])
        
        return recommendations

if __name__ == "__main__":
    # Test the document enhancer
    config = {}
    enhancer = DocumentEnhancer(config)
    
    # Create test document
    test_doc = "test_rebuttal.txt"
    with open(test_doc, 'w') as f:
        f.write("Wayne Gault's Response\\n\\nThis is a test rebuttal document.")
    
    # Test findings
    test_findings = [
        {
            'finding_type': 'age_discrimination',
            'description': 'Age discrimination detected',
            'confidence_score': 0.8,
            'file_path': 'test.txt'
        }
    ]
    
    enhanced_path = enhancer.enhance_document(test_doc, test_findings)
    print(f"Enhanced document created: {enhanced_path}")
    
    # Cleanup
    os.remove(test_doc)
