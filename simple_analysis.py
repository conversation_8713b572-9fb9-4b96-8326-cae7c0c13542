#!/usr/bin/env python3
"""
Simple Legal Rebuttal Analysis System
Minimal dependencies version that works without complex setup
"""

import os
import sys
import json
import re
from datetime import datetime
from typing import List, Dict, Any

def load_config():
    """Load configuration with fallback to defaults"""
    config = {
        'paths': {
            'file_root_directory': 'C:/Users/<USER>/OneDrive/Documents/Work/Investigation',
            'evidence_against': 'Evidence Against Me',
            'references': 'References',
            'rebuttal_document': "<PERSON>'s Response v1.docx",
            'output_directory': 'Generated Output'
        },
        'user_profile': {
            'age': 56,
            'service_years': 30,
            'employer': 'NHS Scotland'
        },
        'patterns': {
            'age_discrimination': [
                'retire', 'retirement', 'too old', 'past it', 'make way', 'younger',
                'fresh blood', 'new perspective', 'outdated', 'behind the times',
                'set in ways', "can't adapt", 'technology', 'digital native', '56'
            ],
            'procedural_violations': [
                'no investigation', 'predetermined', 'biased', 'unfair', 'no evidence',
                'assumption', 'prejudice', 'already decided', 'concluded', 'wrap this up'
            ],
            'positive_evidence': [
                'excellent', 'outstanding', 'exemplary', 'commend', 'praise',
                'valuable', 'asset', 'experience', 'mentor', 'dedicated'
            ]
        }
    }
    
    # Try to load from config file if it exists
    try:
        import yaml
        with open('config.yaml', 'r') as f:
            file_config = yaml.safe_load(f)
            config.update(file_config)
    except:
        print("⚠️ Using default configuration (config.yaml not found or yaml not installed)")
    
    return config

def read_text_file(file_path: str) -> str:
    """Read text content from a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
        except:
            return ""
    except:
        return ""

def analyze_text(text: str, patterns: List[str], analysis_type: str) -> List[Dict]:
    """Analyze text for specific patterns"""
    findings = []
    text_lower = text.lower()
    
    for pattern in patterns:
        if pattern.lower() in text_lower:
            # Find the context around the pattern
            pattern_pos = text_lower.find(pattern.lower())
            start = max(0, pattern_pos - 100)
            end = min(len(text), pattern_pos + len(pattern) + 100)
            context = text[start:end].strip()
            
            finding = {
                'type': analysis_type,
                'pattern': pattern,
                'context': context,
                'confidence': 0.8,  # Simple confidence score
                'timestamp': datetime.now().isoformat()
            }
            findings.append(finding)
    
    return findings

def process_folder(folder_path: str, config: Dict, analysis_mode: str) -> List[Dict]:
    """Process all files in a folder"""
    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return []
    
    all_findings = []
    file_count = 0
    
    print(f"📂 Processing folder: {folder_path}")
    
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            # Only process text files for now (can be extended)
            if file_ext in ['.txt', '.log']:
                print(f"   📄 Processing: {file}")
                content = read_text_file(file_path)
                
                if content:
                    file_count += 1
                    
                    if analysis_mode == 'critique':
                        # Look for age discrimination
                        age_findings = analyze_text(
                            content, 
                            config['patterns']['age_discrimination'],
                            'age_discrimination'
                        )
                        all_findings.extend(age_findings)
                        
                        # Look for procedural violations
                        proc_findings = analyze_text(
                            content,
                            config['patterns']['procedural_violations'],
                            'procedural_violation'
                        )
                        all_findings.extend(proc_findings)
                        
                    elif analysis_mode == 'support':
                        # Look for positive evidence
                        pos_findings = analyze_text(
                            content,
                            config['patterns']['positive_evidence'],
                            'positive_evidence'
                        )
                        all_findings.extend(pos_findings)
                else:
                    print(f"   ⚠️ Could not read content from: {file}")
            else:
                print(f"   ⏭️ Skipping unsupported file type: {file}")
    
    print(f"   ✅ Processed {file_count} files, found {len(all_findings)} findings")
    return all_findings

def generate_report(all_findings: List[Dict], config: Dict) -> str:
    """Generate a simple text report"""
    report = []
    report.append("=" * 80)
    report.append("LEGAL REBUTTAL ANALYSIS REPORT")
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Case: Wayne Gault (Age {config['user_profile']['age']}, {config['user_profile']['service_years']}+ years NHS)")
    report.append("=" * 80)

    # Group findings by type
    findings_by_type = {}
    for finding in all_findings:
        finding_type = finding['type']
        if finding_type not in findings_by_type:
            findings_by_type[finding_type] = []
        findings_by_type[finding_type].append(finding)

    # Age discrimination findings
    if 'age_discrimination' in findings_by_type:
        report.append("\n🚨 AGE DISCRIMINATION FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['age_discrimination'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")

    # Procedural violations
    if 'procedural_violation' in findings_by_type:
        report.append("\n⚖️ PROCEDURAL VIOLATION FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['procedural_violation'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")

    # Positive evidence
    if 'positive_evidence' in findings_by_type:
        report.append("\n📚 SUPPORTING EVIDENCE FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['positive_evidence'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")

    # Summary
    report.append("\n📊 SUMMARY:")
    report.append("-" * 20)
    report.append(f"Total findings: {len(all_findings)}")
    for finding_type, findings in findings_by_type.items():
        report.append(f"{finding_type.replace('_', ' ').title()}: {len(findings)}")

    report.append("\n" + "=" * 80)

    return "\n".join(report)

def main():
    """Main analysis function"""
    print("🏛️  SIMPLE LEGAL REBUTTAL ANALYSIS SYSTEM")
    print("⚖️  Minimal Dependencies Version")
    print("🎯 Wayne Gault - Age 56, 30+ Years NHS Scotland")
    print("=" * 60)

    # Load configuration
    config = load_config()

    # Check file paths
    root_dir = config['paths']['file_root_directory']
    evidence_dir = os.path.join(root_dir, config['paths']['evidence_against'])
    references_dir = os.path.join(root_dir, config['paths']['references'])

    print(f"📁 Root directory: {root_dir}")
    print(f"📂 Evidence Against Me: {evidence_dir}")
    print(f"📂 References: {references_dir}")

    # Check if directories exist
    evidence_exists = os.path.exists(evidence_dir)
    references_exists = os.path.exists(references_dir)

    print(f"   Evidence folder exists: {'✅' if evidence_exists else '❌'}")
    print(f"   References folder exists: {'✅' if references_exists else '❌'}")

    if not evidence_exists and not references_exists:
        print("\n❌ No evidence folders found!")
        print("📋 Please create the following structure:")
        print(f"   {evidence_dir}")
        print(f"   {references_dir}")
        return False

    # Process evidence
    all_findings = []

    if evidence_exists:
        print(f"\n🔍 ANALYZING EVIDENCE AGAINST ME...")
        evidence_findings = process_folder(evidence_dir, config, 'critique')
        all_findings.extend(evidence_findings)

    if references_exists:
        print(f"\n📚 ANALYZING SUPPORTING REFERENCES...")
        reference_findings = process_folder(references_dir, config, 'support')
        all_findings.extend(reference_findings)

    # Generate report
    if all_findings:
        print(f"\n📝 GENERATING REPORT...")
        report = generate_report(all_findings, config)

        # Save report
        output_dir = os.path.join(root_dir, config['paths']['output_directory'])
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(output_dir, f"analysis_report_{timestamp}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"✅ Report saved: {report_file}")
        print(f"\n{report}")

    else:
        print("\n⚠️ No findings detected")
        print("📋 Make sure your evidence files contain text content")

    print("\n" + "=" * 60)
    print("✅ ANALYSIS COMPLETE")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
    """Generate a simple text report"""
    report = []
    report.append("=" * 80)
    report.append("LEGAL REBUTTAL ANALYSIS REPORT")
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"Case: Wayne Gault (Age {config['user_profile']['age']}, {config['user_profile']['service_years']}+ years NHS)")
    report.append("=" * 80)
    
    # Group findings by type
    findings_by_type = {}
    for finding in all_findings:
        finding_type = finding['type']
        if finding_type not in findings_by_type:
            findings_by_type[finding_type] = []
        findings_by_type[finding_type].append(finding)
    
    # Age discrimination findings
    if 'age_discrimination' in findings_by_type:
        report.append("\n🚨 AGE DISCRIMINATION FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['age_discrimination'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")
    
    # Procedural violations
    if 'procedural_violation' in findings_by_type:
        report.append("\n⚖️ PROCEDURAL VIOLATION FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['procedural_violation'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")
    
    # Positive evidence
    if 'positive_evidence' in findings_by_type:
        report.append("\n📚 SUPPORTING EVIDENCE FINDINGS:")
        report.append("-" * 40)
        for i, finding in enumerate(findings_by_type['positive_evidence'], 1):
            report.append(f"{i}. Pattern: '{finding['pattern']}'")
            report.append(f"   Context: {finding['context']}")
            report.append(f"   Confidence: {finding['confidence']:.0%}")
            report.append("")
    
    # Summary
    report.append("\n📊 SUMMARY:")
    report.append("-" * 20)
    report.append(f"Total findings: {len(all_findings)}")
    for finding_type, findings in findings_by_type.items():
        report.append(f"{finding_type.replace('_', ' ').title()}: {len(findings)}")
    
    report.append("\n" + "=" * 80)
    
    return "\n".join(report)

def main():
    """Main analysis function"""
    print("🏛️  SIMPLE LEGAL REBUTTAL ANALYSIS SYSTEM")
    print("⚖️  Minimal Dependencies Version")
    print("🎯 Wayne Gault - Age 56, 30+ Years NHS Scotland")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    
    # Check file paths
    root_dir = config['paths']['file_root_directory']
    evidence_dir = os.path.join(root_dir, config['paths']['evidence_against'])
    references_dir = os.path.join(root_dir, config['paths']['references'])
    
    print(f"📁 Root directory: {root_dir}")
    print(f"📂 Evidence Against Me: {evidence_dir}")
    print(f"📂 References: {references_dir}")
    
    # Check if directories exist
    evidence_exists = os.path.exists(evidence_dir)
    references_exists = os.path.exists(references_dir)
    
    print(f"   Evidence folder exists: {'✅' if evidence_exists else '❌'}")
    print(f"   References folder exists: {'✅' if references_exists else '❌'}")
    
    if not evidence_exists and not references_exists:
        print("\n❌ No evidence folders found!")
        print("📋 Please create the following structure:")
        print(f"   {evidence_dir}")
        print(f"   {references_dir}")
        return False
    
    # Process evidence
    all_findings = []
    
    if evidence_exists:
        print(f"\n🔍 ANALYZING EVIDENCE AGAINST ME...")
        evidence_findings = process_folder(evidence_dir, config, 'critique')
        all_findings.extend(evidence_findings)
    
    if references_exists:
        print(f"\n📚 ANALYZING SUPPORTING REFERENCES...")
        reference_findings = process_folder(references_dir, config, 'support')
        all_findings.extend(reference_findings)
    
    # Generate report
    if all_findings:
        print(f"\n📝 GENERATING REPORT...")
        report = generate_report(all_findings, config)
        
        # Save report
        output_dir = os.path.join(root_dir, config['paths']['output_directory'])
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(output_dir, f"analysis_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ Report saved: {report_file}")
        print(f"\n{report}")
        
    else:
        print("\n⚠️ No findings detected")
        print("📋 Make sure your evidence files contain text content")
    
    print("\n" + "=" * 60)
    print("✅ ANALYSIS COMPLETE")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
