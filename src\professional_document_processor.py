"""
Professional Document Processing with Track Changes and Citations
Advanced Word document integration for legal document enhancement
"""

import os
import shutil
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

# Word document processing
try:
    from docx import Document
    from docx.shared import Inches, RGBColor
    from docx.enum.text import WD_COLOR_INDEX
    DOCX_AVAILABLE = True

    # Optional advanced features
    try:
        from docx.oxml.shared import OxmlElement, qn
        from docx.oxml.ns import nsdecls
        DOCX_ADVANCED = True
    except ImportError:
        DOCX_ADVANCED = False

except ImportError:
    DOCX_AVAILABLE = False
    DOCX_ADVANCED = False

@dataclass
class DocumentEnhancement:
    enhancement_type: str  # 'insertion', 'comment', 'highlight', 'citation'
    content: str
    position: str  # paragraph identifier or section
    legal_basis: str
    confidence_score: float
    evidence_file: str
    recommendation: str

@dataclass
class Citation:
    case_name: str
    citation: str
    relevance: str
    hyperlink_target: Optional[str] = None

class ProfessionalDocumentProcessor:
    """Advanced Word document processor with track changes and legal citations"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.backup_dir = "backups"
        self.output_dir = "Generated Output"
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
    
    def enhance_document_professional(self, document_path: str, enhancements: List[DocumentEnhancement],
                                    legal_citations: List[Citation] = None) -> str:
        """Create professionally enhanced Word document with track changes"""

        if not DOCX_AVAILABLE:
            raise Exception("python-docx library not available for professional document processing")

        # Create backup
        backup_path = self._create_backup(document_path)
        if not backup_path:
            raise Exception("Cannot proceed without backup")

        try:
            # Load document
            doc = Document(document_path)
            
            # Add document metadata
            self._add_document_metadata(doc)
            
            # Process enhancements
            self._apply_enhancements(doc, enhancements)
            
            # Add legal citations section
            if legal_citations:
                self._add_citations_section(doc, legal_citations)
            
            # Add analysis summary section
            self._add_analysis_summary(doc, enhancements)
            
            # Save enhanced document
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            enhanced_filename = f"enhanced_professional_{timestamp}_{os.path.basename(document_path)}"
            enhanced_path = os.path.join(self.output_dir, enhanced_filename)
            
            doc.save(enhanced_path)
            
            logging.info(f"Professional enhanced document created: {enhanced_path}")
            return enhanced_path
            
        except Exception as e:
            logging.error(f"Error creating professional enhanced document: {e}")
            raise
    
    def _create_backup(self, document_path: str) -> str:
        """Create backup of original document"""
        if not os.path.exists(document_path):
            logging.error(f"Document not found: {document_path}")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"backup_professional_{timestamp}_{os.path.basename(document_path)}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        try:
            shutil.copy2(document_path, backup_path)
            logging.info(f"Professional backup created: {backup_path}")
            return backup_path
        except Exception as e:
            logging.error(f"Failed to create backup: {e}")
            return None
    
    def _add_document_metadata(self, doc: Document):
        """Add metadata to document"""
        core_props = doc.core_properties
        core_props.author = "Legal Rebuttal Analysis System"
        core_props.comments = "Enhanced with AI-powered legal analysis"
        core_props.created = datetime.now()
        core_props.modified = datetime.now()
        core_props.title = "Enhanced Legal Rebuttal Document"
        core_props.subject = "Employment Law Defense - Age Discrimination and Procedural Fairness"
    
    def _apply_enhancements(self, doc: Document, enhancements: List[DocumentEnhancement]):
        """Apply enhancements to document with track changes simulation"""
        
        # Add enhancement summary at the beginning
        if doc.paragraphs:
            # Insert after first paragraph (title)
            first_para = doc.paragraphs[0]
            enhancement_para = first_para.insert_paragraph_before()
            enhancement_para.add_run("DOCUMENT ENHANCED WITH AI LEGAL ANALYSIS").bold = True
            enhancement_para.add_run(f"\nEnhanced on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            enhancement_para.add_run(f"\nTotal enhancements: {len(enhancements)}")
            enhancement_para.add_run("\n" + "="*60 + "\n")
        
        # Group enhancements by type
        enhancements_by_type = {}
        for enhancement in enhancements:
            if enhancement.enhancement_type not in enhancements_by_type:
                enhancements_by_type[enhancement.enhancement_type] = []
            enhancements_by_type[enhancement.enhancement_type].append(enhancement)
        
        # Apply each type of enhancement
        for enhancement_type, type_enhancements in enhancements_by_type.items():
            self._apply_enhancement_type(doc, enhancement_type, type_enhancements)
    
    def _apply_enhancement_type(self, doc: Document, enhancement_type: str, enhancements: List[DocumentEnhancement]):
        """Apply specific type of enhancement"""
        
        if enhancement_type == "age_discrimination":
            self._add_age_discrimination_section(doc, enhancements)
        elif enhancement_type == "procedural_violation":
            self._add_procedural_violation_section(doc, enhancements)
        elif enhancement_type == "positive_evidence":
            self._add_positive_evidence_section(doc, enhancements)
        else:
            self._add_general_enhancement_section(doc, enhancement_type, enhancements)
    
    def _add_age_discrimination_section(self, doc: Document, enhancements: List[DocumentEnhancement]):
        """Add age discrimination analysis section"""
        
        # Add section heading
        heading = doc.add_heading("AGE DISCRIMINATION ANALYSIS", level=1)
        heading_run = heading.runs[0]
        heading_run.font.color.rgb = RGBColor(128, 0, 0)  # Dark red
        
        # Add introduction
        intro_para = doc.add_paragraph()
        intro_run = intro_para.add_run("AI Analysis has identified potential age discrimination issues. ")
        intro_run.bold = True
        intro_para.add_run("Wayne's age (56) and long service (30+ years) provide strong legal protections under the Equality Act 2010.")
        
        # Add each finding
        for i, enhancement in enumerate(enhancements, 1):
            self._add_finding_paragraph(doc, f"Age Discrimination Finding #{i}", enhancement)
        
        # Add strategic summary
        strategy_para = doc.add_paragraph()
        strategy_run = strategy_para.add_run("STRATEGIC RECOMMENDATION: ")
        strategy_run.bold = True
        strategy_run.font.color.rgb = RGBColor(0, 0, 128)  # Dark blue
        strategy_para.add_run("Age discrimination should be the primary focus of Wayne's defense. At 56 with 30+ years NHS service, he has maximum protection under equality legislation.")
    
    def _add_procedural_violation_section(self, doc: Document, enhancements: List[DocumentEnhancement]):
        """Add procedural fairness violation section"""
        
        heading = doc.add_heading("PROCEDURAL FAIRNESS VIOLATIONS", level=1)
        heading_run = heading.runs[0]
        heading_run.font.color.rgb = RGBColor(128, 0, 0)
        
        intro_para = doc.add_paragraph()
        intro_run = intro_para.add_run("AI Analysis has detected procedural fairness issues. ")
        intro_run.bold = True
        intro_para.add_run("These violations of natural justice and ACAS Code requirements can invalidate the entire process.")
        
        for i, enhancement in enumerate(enhancements, 1):
            self._add_finding_paragraph(doc, f"Procedural Violation #{i}", enhancement)
        
        strategy_para = doc.add_paragraph()
        strategy_run = strategy_para.add_run("STRATEGIC RECOMMENDATION: ")
        strategy_run.bold = True
        strategy_run.font.color.rgb = RGBColor(0, 0, 128)
        strategy_para.add_run("Procedural violations provide grounds to challenge the entire investigation as fundamentally flawed and biased.")
    
    def _add_positive_evidence_section(self, doc: Document, enhancements: List[DocumentEnhancement]):
        """Add positive evidence section"""
        
        heading = doc.add_heading("SUPPORTING EVIDENCE ANALYSIS", level=1)
        heading_run = heading.runs[0]
        heading_run.font.color.rgb = RGBColor(0, 128, 0)  # Dark green
        
        intro_para = doc.add_paragraph()
        intro_run = intro_para.add_run("AI Analysis has identified strong supporting evidence. ")
        intro_run.bold = True
        intro_para.add_run("This evidence demonstrates Wayne's value, character, and positive contributions to the NHS.")
        
        for i, enhancement in enumerate(enhancements, 1):
            self._add_finding_paragraph(doc, f"Supporting Evidence #{i}", enhancement)
        
        strategy_para = doc.add_paragraph()
        strategy_run = strategy_para.add_run("STRATEGIC RECOMMENDATION: ")
        strategy_run.bold = True
        strategy_run.font.color.rgb = RGBColor(0, 0, 128)
        strategy_para.add_run("Use this positive evidence to counter negative allegations and demonstrate Wayne's institutional value.")
    
    def _add_finding_paragraph(self, doc: Document, title: str, enhancement: DocumentEnhancement):
        """Add individual finding paragraph with formatting"""
        
        # Finding title
        title_para = doc.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.bold = True
        title_run.underline = True
        
        # Confidence score
        confidence_para = doc.add_paragraph()
        confidence_run = confidence_para.add_run(f"Confidence: {enhancement.confidence_score:.1%}")
        if enhancement.confidence_score >= 0.8:
            confidence_run.font.color.rgb = RGBColor(0, 128, 0)  # Green for high confidence
        elif enhancement.confidence_score >= 0.6:
            confidence_run.font.color.rgb = RGBColor(255, 165, 0)  # Orange for medium
        else:
            confidence_run.font.color.rgb = RGBColor(255, 0, 0)  # Red for low
        
        # Description
        desc_para = doc.add_paragraph()
        desc_para.add_run("Description: ").bold = True
        desc_para.add_run(enhancement.content)
        
        # Evidence source
        if enhancement.evidence_file:
            source_para = doc.add_paragraph()
            source_para.add_run("Evidence Source: ").bold = True
            source_para.add_run(enhancement.evidence_file)
        
        # Legal basis
        if enhancement.legal_basis:
            legal_para = doc.add_paragraph()
            legal_para.add_run("Legal Basis: ").bold = True
            legal_para.add_run(enhancement.legal_basis)
        
        # Recommendation
        rec_para = doc.add_paragraph()
        rec_para.add_run("Recommendation: ").bold = True
        rec_run = rec_para.add_run(enhancement.recommendation)
        rec_run.italic = True
        
        # Add separator
        doc.add_paragraph("─" * 60)
    
    def _add_citations_section(self, doc: Document, citations: List[Citation]):
        """Add legal citations section"""
        
        heading = doc.add_heading("LEGAL AUTHORITIES AND CITATIONS", level=1)
        heading_run = heading.runs[0]
        heading_run.font.color.rgb = RGBColor(75, 0, 130)  # Indigo
        
        intro_para = doc.add_paragraph()
        intro_para.add_run("The following legal authorities support Wayne's case:")
        
        for i, citation in enumerate(citations, 1):
            cite_para = doc.add_paragraph()
            cite_para.add_run(f"{i}. ").bold = True
            cite_para.add_run(f"{citation.case_name} {citation.citation}")
            
            if citation.relevance:
                relevance_para = doc.add_paragraph()
                relevance_para.add_run("   Relevance: ").italic = True
                relevance_para.add_run(citation.relevance)
    
    def _add_analysis_summary(self, doc: Document, enhancements: List[DocumentEnhancement]):
        """Add comprehensive analysis summary"""
        
        heading = doc.add_heading("AI ANALYSIS SUMMARY", level=1)
        heading_run = heading.runs[0]
        heading_run.font.color.rgb = RGBColor(0, 0, 0)
        
        # Statistics
        stats_para = doc.add_paragraph()
        stats_para.add_run("Analysis Statistics:").bold = True
        
        enhancement_counts = {}
        high_confidence_count = 0
        
        for enhancement in enhancements:
            enhancement_type = enhancement.enhancement_type
            enhancement_counts[enhancement_type] = enhancement_counts.get(enhancement_type, 0) + 1
            if enhancement.confidence_score >= 0.8:
                high_confidence_count += 1
        
        for enhancement_type, count in enhancement_counts.items():
            stats_para = doc.add_paragraph()
            stats_para.add_run(f"• {enhancement_type.replace('_', ' ').title()}: {count} findings")
        
        confidence_para = doc.add_paragraph()
        confidence_para.add_run(f"• High confidence findings (≥80%): {high_confidence_count}")
        
        # Overall assessment
        assessment_para = doc.add_paragraph()
        assessment_para.add_run("Overall Assessment: ").bold = True
        
        if high_confidence_count >= 3:
            assessment_para.add_run("STRONG CASE - Multiple high-confidence findings support Wayne's defense.")
        elif high_confidence_count >= 1:
            assessment_para.add_run("GOOD CASE - Solid evidence supports Wayne's position.")
        else:
            assessment_para.add_run("DEVELOPING CASE - Evidence provides foundation for defense strategy.")

if __name__ == "__main__":
    # Test the professional document processor
    config = {}
    processor = ProfessionalDocumentProcessor(config)
    
    # Test enhancements
    test_enhancements = [
        DocumentEnhancement(
            enhancement_type="age_discrimination",
            content="Direct reference to Wayne's age in discriminatory context",
            position="paragraph_1",
            legal_basis="Equality Act 2010 Section 13",
            confidence_score=0.85,
            evidence_file="test_email.msg",
            recommendation="Challenge as direct age discrimination"
        )
    ]
    
    print("Professional document processor initialized successfully")
    print(f"Test enhancement: {test_enhancements[0].enhancement_type} at {test_enhancements[0].confidence_score:.1%} confidence")
