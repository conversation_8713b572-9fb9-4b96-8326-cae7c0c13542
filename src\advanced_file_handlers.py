"""
Advanced File Handlers for Legal Rebuttal Analysis System
Full implementation with Excel, Image OCR, and enhanced processing
"""

import os
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import pandas as pd
import numpy as np
from PIL import Image
try:
    import pytesseract
    import cv2
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
from dataclasses import dataclass

@dataclass
class ProcessingResult:
    success: bool
    content: str
    metadata: Dict[str, Any]
    structured_data: Optional[Dict] = None
    error: Optional[str] = None

class AdvancedExcelHandler:
    """Advanced Excel file processing with data analysis"""
    
    def __init__(self):
        self.supported_extensions = ['.xlsx', '.xls', '.xlsm', '.csv']
    
    def extract_content(self, file_path: str) -> ProcessingResult:
        """Extract content from Excel files with advanced analysis"""
        try:
            # Read Excel file with multiple sheets
            excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            
            content_parts = []
            structured_data = {}
            
            for sheet_name, df in excel_data.items():
                content_parts.append(f"=== SHEET: {sheet_name} ===")
                
                # Basic sheet info
                content_parts.append(f"Rows: {len(df)}, Columns: {len(df.columns)}")
                content_parts.append(f"Columns: {', '.join(df.columns.astype(str))}")
                
                # Convert to text representation
                sheet_text = self._dataframe_to_text(df)
                content_parts.append(sheet_text)
                
                # Store structured data for analysis
                structured_data[sheet_name] = {
                    'dataframe': df.to_dict('records'),
                    'summary': self._analyze_sheet_content(df),
                    'potential_evidence': self._identify_potential_evidence(df)
                }
                
                content_parts.append("")  # Separator
            
            # Combine all content
            full_content = "\n".join(content_parts)
            
            # Extract metadata
            metadata = self._extract_excel_metadata(file_path, excel_data)
            
            return ProcessingResult(
                success=True,
                content=full_content,
                metadata=metadata,
                structured_data=structured_data
            )
            
        except Exception as e:
            logging.error(f"Error processing Excel file {file_path}: {e}")
            return ProcessingResult(
                success=False,
                content="",
                metadata={},
                error=str(e)
            )
    
    def _dataframe_to_text(self, df: pd.DataFrame) -> str:
        """Convert DataFrame to readable text"""
        if df.empty:
            return "Empty sheet"
        
        # Limit size for readability
        max_rows = 50
        max_cols = 10
        
        display_df = df.head(max_rows).iloc[:, :max_cols]
        
        # Convert to string with proper formatting
        text_repr = display_df.to_string(index=False, max_rows=max_rows)
        
        if len(df) > max_rows:
            text_repr += f"\n... ({len(df) - max_rows} more rows)"
        
        if len(df.columns) > max_cols:
            text_repr += f"\n... ({len(df.columns) - max_cols} more columns)"
        
        return text_repr
    
    def _analyze_sheet_content(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze sheet content for legal relevance"""
        analysis = {
            'row_count': len(df),
            'column_count': len(df.columns),
            'has_dates': False,
            'has_names': False,
            'has_financial_data': False,
            'potential_timeline': False,
            'potential_performance_data': False
        }
        
        # Check for date columns
        for col in df.columns:
            if df[col].dtype == 'datetime64[ns]' or 'date' in str(col).lower():
                analysis['has_dates'] = True
                analysis['potential_timeline'] = True
        
        # Check for name-like columns
        name_indicators = ['name', 'employee', 'staff', 'person', 'individual']
        for col in df.columns:
            if any(indicator in str(col).lower() for indicator in name_indicators):
                analysis['has_names'] = True
        
        # Check for financial data
        financial_indicators = ['salary', 'pay', 'cost', 'budget', 'amount', '£', '$']
        for col in df.columns:
            col_str = str(col).lower()
            if any(indicator in col_str for indicator in financial_indicators):
                analysis['has_financial_data'] = True
        
        # Check for performance indicators
        performance_indicators = ['performance', 'rating', 'score', 'review', 'appraisal']
        for col in df.columns:
            col_str = str(col).lower()
            if any(indicator in col_str for indicator in performance_indicators):
                analysis['potential_performance_data'] = True
        
        return analysis
    
    def _identify_potential_evidence(self, df: pd.DataFrame) -> List[str]:
        """Identify potential evidence in the spreadsheet"""
        evidence_types = []
        
        # Check column names for evidence indicators
        evidence_indicators = {
            'timeline': ['date', 'time', 'when', 'period'],
            'performance': ['performance', 'rating', 'score', 'review', 'appraisal', 'target'],
            'financial': ['salary', 'pay', 'cost', 'budget', 'overtime', 'hours'],
            'disciplinary': ['disciplinary', 'warning', 'misconduct', 'breach', 'violation'],
            'attendance': ['attendance', 'absence', 'sick', 'leave', 'holiday'],
            'training': ['training', 'development', 'course', 'qualification', 'skill']
        }
        
        for evidence_type, indicators in evidence_indicators.items():
            for col in df.columns:
                col_str = str(col).lower()
                if any(indicator in col_str for indicator in indicators):
                    evidence_types.append(f"Potential {evidence_type} evidence in column: {col}")
        
        return evidence_types
    
    def _extract_excel_metadata(self, file_path: str, excel_data: Dict) -> Dict[str, Any]:
        """Extract metadata from Excel file"""
        try:
            stat = os.stat(file_path)
            return {
                'file_size': stat.st_size,
                'created_date': datetime.fromtimestamp(stat.st_ctime),
                'modified_date': datetime.fromtimestamp(stat.st_mtime),
                'sheet_count': len(excel_data),
                'sheet_names': list(excel_data.keys()),
                'total_rows': sum(len(df) for df in excel_data.values()),
                'total_columns': sum(len(df.columns) for df in excel_data.values()),
                'file_extension': os.path.splitext(file_path)[1].lower()
            }
        except Exception as e:
            logging.error(f"Error extracting Excel metadata: {e}")
            return {}

class AdvancedImageHandler:
    """Advanced image processing with OCR and legal document analysis"""
    
    def __init__(self):
        self.supported_extensions = ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif']
        # Configure Tesseract for better accuracy
        self.tesseract_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,!?:;-()[]{}"\' '
    
    def extract_content(self, file_path: str) -> ProcessingResult:
        """Extract text from images using advanced OCR"""
        if not OCR_AVAILABLE:
            return ProcessingResult(
                success=False,
                content="",
                metadata={},
                error="OCR libraries (pytesseract, opencv) not available"
            )

        try:
            # Load and preprocess image
            image = cv2.imread(file_path)
            if image is None:
                raise ValueError("Could not load image")
            
            # Preprocess for better OCR
            processed_image = self._preprocess_image(image)
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(processed_image, config=self.tesseract_config)
            
            # Clean and format text
            cleaned_text = self._clean_ocr_text(text)
            
            # Analyze image for document type
            document_analysis = self._analyze_document_type(cleaned_text, image)
            
            # Extract metadata
            metadata = self._extract_image_metadata(file_path, image)
            
            return ProcessingResult(
                success=True,
                content=cleaned_text,
                metadata=metadata,
                structured_data={
                    'document_analysis': document_analysis,
                    'confidence_score': self._calculate_ocr_confidence(text),
                    'preprocessing_applied': True
                }
            )
            
        except Exception as e:
            logging.error(f"Error processing image {file_path}: {e}")
            return ProcessingResult(
                success=False,
                content="",
                metadata={},
                error=str(e)
            )
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR accuracy"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphological operations to clean up
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _clean_ocr_text(self, text: str) -> str:
        """Clean and format OCR extracted text"""
        if not text.strip():
            return "No text detected in image"
        
        # Remove excessive whitespace
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Join lines with proper spacing
        cleaned = '\n'.join(lines)
        
        # Fix common OCR errors
        replacements = {
            '|': 'I',
            '0': 'O',  # Context-dependent
            '5': 'S',  # Context-dependent
            '1': 'l',  # Context-dependent
        }
        
        # Apply replacements cautiously (only in specific contexts)
        # This is a simplified version - production would use more sophisticated correction
        
        return cleaned
    
    def _analyze_document_type(self, text: str, image: np.ndarray) -> Dict[str, Any]:
        """Analyze what type of document this might be"""
        analysis = {
            'document_type': 'unknown',
            'confidence': 0.0,
            'indicators': []
        }
        
        text_lower = text.lower()
        
        # Check for email indicators
        email_indicators = ['from:', 'to:', 'subject:', 'date:', '@', 'sent:', 'received:']
        email_score = sum(1 for indicator in email_indicators if indicator in text_lower)
        
        # Check for letter indicators
        letter_indicators = ['dear', 'sincerely', 'yours faithfully', 'regards', 'signature']
        letter_score = sum(1 for indicator in letter_indicators if indicator in text_lower)
        
        # Check for form indicators
        form_indicators = ['form', 'application', 'checkbox', 'tick', 'select', 'please complete']
        form_score = sum(1 for indicator in form_indicators if indicator in text_lower)
        
        # Check for legal document indicators
        legal_indicators = ['whereas', 'therefore', 'pursuant', 'hereby', 'clause', 'section']
        legal_score = sum(1 for indicator in legal_indicators if indicator in text_lower)
        
        # Determine document type
        scores = {
            'email': email_score,
            'letter': letter_score,
            'form': form_score,
            'legal_document': legal_score
        }
        
        if max(scores.values()) > 0:
            analysis['document_type'] = max(scores, key=scores.get)
            analysis['confidence'] = min(scores[analysis['document_type']] / 10.0, 1.0)
            analysis['indicators'] = [k for k, v in scores.items() if v > 0]
        
        return analysis
    
    def _calculate_ocr_confidence(self, text: str) -> float:
        """Calculate confidence score for OCR extraction"""
        if not text.strip():
            return 0.0
        
        # Simple heuristics for confidence
        confidence = 0.5  # Base confidence
        
        # Boost for longer text
        if len(text) > 100:
            confidence += 0.2
        
        # Boost for proper sentence structure
        sentences = text.split('.')
        if len(sentences) > 2:
            confidence += 0.1
        
        # Reduce for excessive special characters (OCR errors)
        special_char_ratio = sum(1 for c in text if not c.isalnum() and c not in ' .,!?:;-()[]{}"\'\n') / len(text)
        if special_char_ratio > 0.1:
            confidence -= 0.2
        
        return max(0.0, min(1.0, confidence))
    
    def _extract_image_metadata(self, file_path: str, image: np.ndarray) -> Dict[str, Any]:
        """Extract metadata from image file"""
        try:
            stat = os.stat(file_path)
            height, width = image.shape[:2]
            
            return {
                'file_size': stat.st_size,
                'created_date': datetime.fromtimestamp(stat.st_ctime),
                'modified_date': datetime.fromtimestamp(stat.st_mtime),
                'image_width': width,
                'image_height': height,
                'image_channels': len(image.shape),
                'file_extension': os.path.splitext(file_path)[1].lower(),
                'estimated_dpi': self._estimate_dpi(image)
            }
        except Exception as e:
            logging.error(f"Error extracting image metadata: {e}")
            return {}
    
    def _estimate_dpi(self, image: np.ndarray) -> int:
        """Estimate DPI of image (rough approximation)"""
        height, width = image.shape[:2]
        
        # Rough estimation based on typical document sizes
        # A4 at 300 DPI would be approximately 2480x3508 pixels
        if width > 2000 and height > 2500:
            return 300
        elif width > 1000 and height > 1400:
            return 150
        else:
            return 72  # Screen resolution

if __name__ == "__main__":
    # Test the advanced handlers
    excel_handler = AdvancedExcelHandler()
    image_handler = AdvancedImageHandler()
    
    print("Advanced file handlers initialized successfully")
    print(f"Excel supported: {excel_handler.supported_extensions}")
    print(f"Image supported: {image_handler.supported_extensions}")
