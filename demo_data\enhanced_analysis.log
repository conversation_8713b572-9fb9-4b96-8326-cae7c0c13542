2025-09-01 19:54:13,950 - INFO - Database initialized successfully
2025-09-01 19:54:13,950 - INFO - Training age_discrimination model with synthetic data
2025-09-01 19:54:14,272 - INFO - Age discrimination model trained: {'train_accuracy': 1.0, 'test_accuracy': 0.6666666666666666, 'cv_mean': np.float64(0.9), 'cv_std': np.float64(0.2), 'training_samples': 14, 'feature_count': 193}
2025-09-01 19:54:14,275 - INFO - Model age_discrimination_detector saved to models/age_discrimination_model.pkl
2025-09-01 19:54:14,275 - INFO - Training procedural_fairness model with synthetic data
2025-09-01 19:54:14,376 - INFO - Model procedural_fairness_detector saved to models/procedural_fairness_model.pkl
2025-09-01 19:54:14,376 - INFO - ML models initialized successfully
2025-09-01 19:54:14,427 - INFO - Legal knowledge database initialized
2025-09-01 19:54:14,440 - INFO - Legal knowledge base populated with initial data
2025-09-01 19:54:14,455 - INFO - Database cleared
2025-09-01 19:54:14,776 - INFO - Professional backup created: backups/backup_professional_20250901_195414_Wayne Gault's Response v1.docx
2025-09-01 19:54:14,776 - ERROR - Error creating professional enhanced document: Package not found at 'Wayne Gault's Response v1.docx'
