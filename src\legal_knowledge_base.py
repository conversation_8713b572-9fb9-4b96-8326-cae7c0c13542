"""
Legal Knowledge Base for Employment Law Analysis
Comprehensive database of case law, statutes, and NHS Scotland policies
"""

import sqlite3
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class LegalCase:
    case_name: str
    citation: str
    year: int
    court: str
    summary: str
    legal_principle: str
    relevance_score: float
    keywords: List[str]
    case_type: str  # 'age_discrimination', 'unfair_dismissal', 'procedural_fairness'

@dataclass
class Statute:
    name: str
    section: str
    text: str
    summary: str
    relevance_keywords: List[str]
    statute_type: str

@dataclass
class NHSPolicy:
    policy_name: str
    section: str
    requirement: str
    compliance_indicator: str
    breach_consequences: str
    relevance_keywords: List[str]

class LegalKnowledgeBase:
    """Comprehensive legal knowledge database"""
    
    def __init__(self, db_path: str = "legal_knowledge.db"):
        self.db_path = db_path
        try:
            self._init_database()
            self._populate_initial_data()
        except Exception as e:
            logging.error(f"Error initializing legal knowledge base: {e}")
            # Continue with empty database
    
    def _init_database(self):
        """Initialize legal knowledge database"""
        with sqlite3.connect(self.db_path) as conn:
            # Case law table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS case_law (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_name TEXT NOT NULL,
                    citation TEXT NOT NULL,
                    year INTEGER,
                    court TEXT,
                    summary TEXT,
                    legal_principle TEXT,
                    relevance_score REAL,
                    keywords TEXT,
                    case_type TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Statutes table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS statutes (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    section TEXT,
                    text TEXT,
                    summary TEXT,
                    relevance_keywords TEXT,
                    statute_type TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # NHS policies table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS nhs_policies (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    policy_name TEXT NOT NULL,
                    section TEXT,
                    requirement TEXT,
                    compliance_indicator TEXT,
                    breach_consequences TEXT,
                    relevance_keywords TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Vento bands table (compensation guidelines)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS vento_bands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    band_name TEXT NOT NULL,
                    lower_amount INTEGER,
                    upper_amount INTEGER,
                    description TEXT,
                    applicable_cases TEXT,
                    year_updated INTEGER
                )
            """)
            
            conn.commit()
            logging.info("Legal knowledge database initialized")
    
    def _populate_initial_data(self):
        """Populate database with initial legal knowledge"""
        # Check if data already exists
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM case_law")
                if cursor.fetchone()[0] > 0:
                    return  # Data already populated
        except sqlite3.OperationalError:
            # Table doesn't exist yet, continue with population
            pass
        
        # Add key age discrimination cases
        age_discrimination_cases = [
            LegalCase(
                case_name="Seldon v Clarkson Wright and Jakes",
                citation="[2012] UKSC 16",
                year=2012,
                court="Supreme Court",
                summary="Mandatory retirement age can be justified if it's a proportionate means of achieving a legitimate aim",
                legal_principle="Age discrimination can be justified in limited circumstances with proper evidence",
                relevance_score=0.9,
                keywords=["retirement", "age", "justification", "proportionate"],
                case_type="age_discrimination"
            ),
            LegalCase(
                case_name="Homer v Chief Constable of West Yorkshire",
                citation="[2012] UKSC 15",
                year=2012,
                court="Supreme Court",
                summary="Age limit for law degree requirement constituted indirect age discrimination",
                legal_principle="Indirect age discrimination occurs when neutral policies disproportionately affect older workers",
                relevance_score=0.85,
                keywords=["indirect", "age", "qualification", "disproportionate"],
                case_type="age_discrimination"
            ),
            LegalCase(
                case_name="Woodcock v Cumbria Primary Care Trust",
                citation="[2012] EWCA Civ 330",
                year=2012,
                court="Court of Appeal",
                summary="Comments about retirement and age-related assumptions constituted direct age discrimination",
                legal_principle="Age-related comments in employment decisions constitute direct discrimination",
                relevance_score=0.9,
                keywords=["retirement", "comments", "assumptions", "direct"],
                case_type="age_discrimination"
            )
        ]
        
        # Add key statutes
        key_statutes = [
            Statute(
                name="Equality Act 2010",
                section="Section 13 - Direct Discrimination",
                text="A person (A) discriminates against another (B) if, because of a protected characteristic, A treats B less favourably than A treats or would treat others.",
                summary="Defines direct discrimination including age discrimination",
                relevance_keywords=["age", "discrimination", "protected", "characteristic"],
                statute_type="discrimination"
            ),
            Statute(
                name="Equality Act 2010",
                section="Section 19 - Indirect Discrimination",
                text="A person (A) discriminates against another (B) if A applies a provision, criterion or practice which puts persons sharing B's protected characteristic at a particular disadvantage.",
                summary="Defines indirect discrimination affecting groups with protected characteristics",
                relevance_keywords=["indirect", "provision", "criterion", "disadvantage"],
                statute_type="discrimination"
            ),
            Statute(
                name="Employment Rights Act 1996",
                section="Section 98 - General",
                text="In determining whether the dismissal of an employee is fair or unfair, it is for the employer to show the reason for the dismissal.",
                summary="Employer must show fair reason for dismissal",
                relevance_keywords=["dismissal", "fair", "reason", "employer"],
                statute_type="unfair_dismissal"
            )
        ]
        
        # Add NHS Scotland policies
        nhs_policies = [
            NHSPolicy(
                policy_name="Staff Governance Standards",
                section="Standard 1 - Well Informed",
                requirement="Staff should be well informed about the organisation's aims, objectives and values",
                compliance_indicator="Clear communication of policies and procedures",
                breach_consequences="Failure to inform staff properly can invalidate disciplinary processes",
                relevance_keywords=["informed", "communication", "policies", "procedures"]
            ),
            NHSPolicy(
                policy_name="Dignity at Work Policy",
                section="Bullying and Harassment",
                requirement="All staff have the right to be treated with dignity and respect",
                compliance_indicator="Zero tolerance approach to bullying and harassment",
                breach_consequences="Failure to address bullying can lead to constructive dismissal claims",
                relevance_keywords=["dignity", "respect", "bullying", "harassment"]
            ),
            NHSPolicy(
                policy_name="Public Sector Equality Duty",
                section="Section 149 Equality Act 2010",
                requirement="Public bodies must have due regard to eliminating discrimination and advancing equality",
                compliance_indicator="Equality impact assessments and monitoring",
                breach_consequences="Failure to consider equality duties can invalidate decisions",
                relevance_keywords=["equality", "discrimination", "due regard", "public sector"]
            )
        ]
        
        # Add Vento bands (compensation guidelines)
        vento_bands = [
            ("Lower Band", 1100, 11200, "Less serious cases, single incidents", "minor discrimination", 2023),
            ("Middle Band", 11200, 33600, "Serious cases without particular aggravating features", "standard discrimination", 2023),
            ("Upper Band", 33600, 56000, "Most serious cases, significant impact on claimant", "severe discrimination", 2023),
            ("Exceptional", 56000, 100000, "Exceptional cases with severe long-term effects", "exceptional discrimination", 2023)
        ]
        
        # Insert data into database
        with sqlite3.connect(self.db_path) as conn:
            # Insert case law
            for case in age_discrimination_cases:
                conn.execute("""
                    INSERT INTO case_law (case_name, citation, year, court, summary, legal_principle, 
                                        relevance_score, keywords, case_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (case.case_name, case.citation, case.year, case.court, case.summary,
                      case.legal_principle, case.relevance_score, json.dumps(case.keywords), case.case_type))
            
            # Insert statutes
            for statute in key_statutes:
                conn.execute("""
                    INSERT INTO statutes (name, section, text, summary, relevance_keywords, statute_type)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (statute.name, statute.section, statute.text, statute.summary,
                      json.dumps(statute.relevance_keywords), statute.statute_type))
            
            # Insert NHS policies
            for policy in nhs_policies:
                conn.execute("""
                    INSERT INTO nhs_policies (policy_name, section, requirement, compliance_indicator,
                                            breach_consequences, relevance_keywords)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (policy.policy_name, policy.section, policy.requirement, policy.compliance_indicator,
                      policy.breach_consequences, json.dumps(policy.relevance_keywords)))
            
            # Insert Vento bands
            for band_name, lower, upper, description, applicable, year in vento_bands:
                conn.execute("""
                    INSERT INTO vento_bands (band_name, lower_amount, upper_amount, description, 
                                           applicable_cases, year_updated)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (band_name, lower, upper, description, applicable, year))
            
            conn.commit()
            logging.info("Legal knowledge base populated with initial data")
    
    def search_case_law(self, keywords: List[str], case_type: str = None) -> List[LegalCase]:
        """Search for relevant case law"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            # Build search query
            query = "SELECT * FROM case_law WHERE "
            params = []
            
            # Add keyword search
            keyword_conditions = []
            for keyword in keywords:
                keyword_conditions.append("(keywords LIKE ? OR summary LIKE ? OR legal_principle LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%", f"%{keyword}%"])
            
            query += "(" + " OR ".join(keyword_conditions) + ")"
            
            # Add case type filter if specified
            if case_type:
                query += " AND case_type = ?"
                params.append(case_type)
            
            query += " ORDER BY relevance_score DESC"
            
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            cases = []
            for row in rows:
                cases.append(LegalCase(
                    case_name=row['case_name'],
                    citation=row['citation'],
                    year=row['year'],
                    court=row['court'],
                    summary=row['summary'],
                    legal_principle=row['legal_principle'],
                    relevance_score=row['relevance_score'],
                    keywords=json.loads(row['keywords']),
                    case_type=row['case_type']
                ))
            
            return cases
    
    def get_vento_bands(self) -> List[Dict]:
        """Get current Vento band compensation guidelines"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM vento_bands 
                ORDER BY lower_amount ASC
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def search_nhs_policies(self, keywords: List[str]) -> List[NHSPolicy]:
        """Search NHS Scotland policies"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            query = """
                SELECT * FROM nhs_policies WHERE 
                relevance_keywords LIKE ? OR requirement LIKE ? OR compliance_indicator LIKE ?
                ORDER BY policy_name
            """
            
            policies = []
            for keyword in keywords:
                pattern = f"%{keyword}%"
                cursor = conn.execute(query, (pattern, pattern, pattern))
                
                for row in cursor.fetchall():
                    policies.append(NHSPolicy(
                        policy_name=row['policy_name'],
                        section=row['section'],
                        requirement=row['requirement'],
                        compliance_indicator=row['compliance_indicator'],
                        breach_consequences=row['breach_consequences'],
                        relevance_keywords=json.loads(row['relevance_keywords'])
                    ))
            
            return policies
    
    def calculate_remedy_estimate(self, case_type: str, severity: str, service_years: int, age: int) -> Dict:
        """Calculate estimated remedy amounts"""
        vento_bands = self.get_vento_bands()
        
        # Base Vento band selection
        if severity == "minor":
            base_band = vento_bands[0]  # Lower band
        elif severity == "standard":
            base_band = vento_bands[1]  # Middle band
        elif severity == "severe":
            base_band = vento_bands[2]  # Upper band
        else:
            base_band = vento_bands[3]  # Exceptional
        
        # Calculate injury to feelings award
        injury_to_feelings = (base_band['lower_amount'] + base_band['upper_amount']) / 2
        
        # Age-related uplift (higher awards for older employees)
        if age >= 55:
            injury_to_feelings *= 1.2
        elif age >= 50:
            injury_to_feelings *= 1.1
        
        # Long service uplift
        if service_years >= 30:
            injury_to_feelings *= 1.15
        elif service_years >= 20:
            injury_to_feelings *= 1.1
        
        # Calculate other potential awards
        statutory_redundancy = min(service_years * 571, 17130)  # 2023 rates
        notice_pay = max(12, service_years) * 571  # Estimated weekly pay
        
        return {
            'injury_to_feelings': round(injury_to_feelings),
            'statutory_redundancy': statutory_redundancy,
            'notice_pay': notice_pay,
            'total_estimate': round(injury_to_feelings + statutory_redundancy + notice_pay),
            'vento_band': base_band['band_name'],
            'age_uplift_applied': age >= 50,
            'long_service_uplift_applied': service_years >= 20
        }

if __name__ == "__main__":
    # Test the legal knowledge base
    kb = LegalKnowledgeBase()
    
    # Test case law search
    age_cases = kb.search_case_law(["age", "discrimination"], "age_discrimination")
    print(f"Found {len(age_cases)} age discrimination cases")
    
    # Test remedy calculation
    remedy = kb.calculate_remedy_estimate("age_discrimination", "standard", 30, 56)
    print(f"Estimated remedy for Wayne: £{remedy['total_estimate']:,}")
    
    # Test NHS policy search
    policies = kb.search_nhs_policies(["dignity", "equality"])
    print(f"Found {len(policies)} relevant NHS policies")
    
    print("Legal knowledge base test completed successfully")
