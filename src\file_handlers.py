"""
File Handlers for Legal Rebuttal Analysis System
Simple implementations for 24-hour MVP
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

# For MSG files - Full implementation with extract-msg library
def extract_msg_content(file_path: str) -> str:
    """Extract content from MSG file using extract-msg library"""
    try:
        import extract_msg

        # Open MSG file
        msg = extract_msg.Message(file_path)

        # Extract all components
        content_parts = []

        # Basic email headers
        if msg.sender:
            content_parts.append(f"From: {msg.sender}")
        if msg.to:
            content_parts.append(f"To: {msg.to}")
        if msg.cc:
            content_parts.append(f"CC: {msg.cc}")
        if msg.subject:
            content_parts.append(f"Subject: {msg.subject}")
        if msg.date:
            content_parts.append(f"Date: {msg.date}")

        content_parts.append("")  # Separator

        # Email body
        if msg.body:
            content_parts.append("Email Body:")
            content_parts.append(msg.body)
        elif msg.htmlBody:
            # If no plain text, try to extract from HTML
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(msg.htmlBody, 'html.parser')
            content_parts.append("Email Body (from HTML):")
            content_parts.append(soup.get_text())

        # Attachments info
        if msg.attachments:
            content_parts.append(f"\nAttachments ({len(msg.attachments)}):")
            for i, attachment in enumerate(msg.attachments):
                if hasattr(attachment, 'longFilename') and attachment.longFilename:
                    content_parts.append(f"  {i+1}. {attachment.longFilename}")
                elif hasattr(attachment, 'shortFilename') and attachment.shortFilename:
                    content_parts.append(f"  {i+1}. {attachment.shortFilename}")

        msg.close()
        return "\n".join(content_parts)

    except ImportError:
        logging.warning("extract-msg library not available, falling back to basic extraction")
        return extract_msg_content_fallback(file_path)
    except Exception as e:
        logging.error(f"Error reading MSG file {file_path}: {e}")
        return f"Error reading MSG file: {str(e)}"

def extract_msg_content_fallback(file_path: str) -> str:
    """Fallback MSG extraction for when extract-msg is not available"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
            # Simple text extraction - look for readable text
            text_content = content.decode('utf-8', errors='ignore')
            return text_content
    except Exception as e:
        logging.error(f"Error in MSG fallback extraction {file_path}: {e}")
        return f"Error reading MSG file: {str(e)}"

# For PDF files
def extract_pdf_content(file_path: str) -> str:
    """Extract content from PDF file"""
    try:
        import PyPDF2
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
            return text
    except ImportError:
        logging.error("PyPDF2 not installed")
        return "PyPDF2 library not available"
    except Exception as e:
        logging.error(f"Error reading PDF file {file_path}: {e}")
        return f"Error reading PDF file: {str(e)}"

# For DOCX files
def extract_docx_content(file_path: str) -> str:
    """Extract content from DOCX file"""
    try:
        from docx import Document
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except ImportError:
        logging.error("python-docx not installed")
        return "python-docx library not available"
    except Exception as e:
        logging.error(f"Error reading DOCX file {file_path}: {e}")
        return f"Error reading DOCX file: {str(e)}"

class FileHandler(ABC):
    """Abstract base class for file handlers"""
    
    @abstractmethod
    def extract_content(self, file_path: str) -> str:
        pass
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """Get basic file metadata"""
        try:
            stat = os.stat(file_path)
            return {
                'file_size': stat.st_size,
                'created_date': datetime.fromtimestamp(stat.st_ctime),
                'modified_date': datetime.fromtimestamp(stat.st_mtime),
                'file_extension': os.path.splitext(file_path)[1].lower()
            }
        except Exception as e:
            logging.error(f"Error getting metadata for {file_path}: {e}")
            return {}

class MSGHandler(FileHandler):
    """Handler for MSG (Outlook email) files"""
    
    def extract_content(self, file_path: str) -> str:
        return extract_msg_content(file_path)

class PDFHandler(FileHandler):
    """Handler for PDF files"""
    
    def extract_content(self, file_path: str) -> str:
        return extract_pdf_content(file_path)

class DOCXHandler(FileHandler):
    """Handler for DOCX files"""
    
    def extract_content(self, file_path: str) -> str:
        return extract_docx_content(file_path)

class TextHandler(FileHandler):
    """Handler for plain text files"""
    
    def extract_content(self, file_path: str) -> str:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='latin-1') as f:
                    return f.read()
            except Exception as e:
                logging.error(f"Error reading text file {file_path}: {e}")
                return f"Error reading text file: {str(e)}"
        except Exception as e:
            logging.error(f"Error reading text file {file_path}: {e}")
            return f"Error reading text file: {str(e)}"

class FileHandlerFactory:
    """Factory for creating appropriate file handlers"""

    _handlers = {
        '.msg': MSGHandler,
        '.pdf': PDFHandler,
        '.docx': DOCXHandler,
        '.txt': TextHandler,
        '.doc': TextHandler,  # Fallback for older Word docs
        # Advanced handlers (imported dynamically)
        '.xlsx': 'AdvancedExcelHandler',
        '.xls': 'AdvancedExcelHandler',
        '.xlsm': 'AdvancedExcelHandler',
        '.csv': 'AdvancedExcelHandler',
        '.png': 'AdvancedImageHandler',
        '.jpg': 'AdvancedImageHandler',
        '.jpeg': 'AdvancedImageHandler',
        '.tiff': 'AdvancedImageHandler',
        '.bmp': 'AdvancedImageHandler',
        '.gif': 'AdvancedImageHandler',
    }
    
    @classmethod
    def get_handler(cls, file_path: str) -> Optional[FileHandler]:
        """Get appropriate handler for file type"""
        ext = os.path.splitext(file_path)[1].lower()
        handler_class = cls._handlers.get(ext)

        if handler_class:
            # Handle string references to advanced handlers
            if isinstance(handler_class, str):
                try:
                    from advanced_file_handlers import AdvancedExcelHandler, AdvancedImageHandler
                    if handler_class == 'AdvancedExcelHandler':
                        return AdvancedExcelHandler()
                    elif handler_class == 'AdvancedImageHandler':
                        return AdvancedImageHandler()
                except ImportError as e:
                    logging.warning(f"Advanced handler {handler_class} not available: {e}")
                    return None
            else:
                return handler_class()
        else:
            logging.warning(f"No handler available for file type: {ext}")
            return None
    
    @classmethod
    def supported_extensions(cls) -> list:
        """Get list of supported file extensions"""
        return list(cls._handlers.keys())

def process_file(file_path: str) -> Dict[str, Any]:
    """Process a single file and extract content and metadata"""
    handler = FileHandlerFactory.get_handler(file_path)
    if not handler:
        return {
            'file_path': file_path,
            'content': '',
            'metadata': {},
            'error': f'Unsupported file type: {os.path.splitext(file_path)[1]}'
        }
    
    try:
        content = handler.extract_content(file_path)
        metadata = handler.get_metadata(file_path)
        
        return {
            'file_path': file_path,
            'content': content,
            'metadata': metadata,
            'error': None
        }
    except Exception as e:
        logging.error(f"Error processing file {file_path}: {e}")
        return {
            'file_path': file_path,
            'content': '',
            'metadata': {},
            'error': str(e)
        }

if __name__ == "__main__":
    # Test file handlers
    print("Supported file types:", FileHandlerFactory.supported_extensions())
    
    # Test with a text file
    test_content = "This is a test file for age discrimination analysis."
    with open("test.txt", "w") as f:
        f.write(test_content)
    
    result = process_file("test.txt")
    print("Test result:", result)
    
    os.remove("test.txt")
    print("File handlers test completed")
