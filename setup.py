"""
Setup script for Legal Rebuttal Analysis System
24-Hour MVP Implementation
"""

from setuptools import setup, find_packages

setup(
    name="legal-rebuttal-analysis",
    version="1.0.0-mvp",
    description="AI-powered legal rebuttal analysis system for <PERSON>'s NHS Scotland employment case",
    author="Legal Analysis Team",
    packages=find_packages(),
    install_requires=[
        "extract-msg>=0.41.1",
        "PyPDF2>=3.0.1", 
        "python-docx>=0.8.11",
        "click>=8.1.7",
        "tqdm>=4.66.1",
        "nltk>=3.8.1",
        "spacy>=3.6.1",
        "pyyaml>=6.0.1",
        "python-dateutil>=2.8.2",
        "pytest>=7.4.0",
        "pytest-cov>=4.1.0"
    ],
    entry_points={
        'console_scripts': [
            'legal-analysis=src.cli:cli',
        ],
    },
    python_requires='>=3.8',
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Legal",
        "Topic :: Office/Business :: Legal",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Programming Language :: Python :: 3.13",
    ],
)
