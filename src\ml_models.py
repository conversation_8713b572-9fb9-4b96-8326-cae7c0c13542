"""
Machine Learning Models for Legal Analysis
Advanced ML implementation replacing simple pattern matching
"""

import os
import pickle
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime

# ML Libraries
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler

# NLP Libraries (optional)
try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

@dataclass
class MLPrediction:
    prediction: str
    confidence: float
    probabilities: Dict[str, float]
    features_used: List[str]
    model_version: str

@dataclass
class TrainingData:
    text: str
    label: str
    metadata: Dict[str, Any]

class LegalMLModel:
    """Base class for legal ML models"""
    
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.model = None
        self.vectorizer = None
        self.is_trained = False
        self.model_version = "1.0"
        self.feature_names = []
        
    def train(self, training_data: List[TrainingData]) -> Dict[str, Any]:
        """Train the model with provided data"""
        raise NotImplementedError
    
    def predict(self, text: str, context: Dict = None) -> MLPrediction:
        """Make prediction on text"""
        raise NotImplementedError
    
    def save_model(self, path: str):
        """Save trained model to disk"""
        model_data = {
            'model': self.model,
            'vectorizer': self.vectorizer,
            'model_name': self.model_name,
            'model_version': self.model_version,
            'feature_names': self.feature_names,
            'is_trained': self.is_trained
        }
        
        with open(path, 'wb') as f:
            pickle.dump(model_data, f)
        
        logging.info(f"Model {self.model_name} saved to {path}")
    
    def load_model(self, path: str):
        """Load trained model from disk"""
        try:
            with open(path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.vectorizer = model_data['vectorizer']
            self.model_name = model_data['model_name']
            self.model_version = model_data['model_version']
            self.feature_names = model_data['feature_names']
            self.is_trained = model_data['is_trained']
            
            logging.info(f"Model {self.model_name} loaded from {path}")
            
        except Exception as e:
            logging.error(f"Error loading model from {path}: {e}")
            self.is_trained = False

class AgeDiscriminationModel(LegalMLModel):
    """Advanced ML model for age discrimination detection"""
    
    def __init__(self):
        super().__init__("age_discrimination_detector")
        self.age_keywords = [
            'retire', 'retirement', 'too old', 'past it', 'make way', 'younger',
            'fresh blood', 'new perspective', 'outdated', 'behind times',
            'set in ways', 'cant adapt', 'technology', 'digital native',
            'old school', 'modern approach', 'pension', 'age', 'experienced'
        ]
        
        # Initialize with pre-trained sentence transformer for semantic understanding
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self.sentence_model = SentenceTransformer('all-MiniLM-L6-v2')
            except:
                logging.warning("SentenceTransformer model not available, using TF-IDF only")
                self.sentence_model = None
        else:
            self.sentence_model = None
    
    def train(self, training_data: List[TrainingData]) -> Dict[str, Any]:
        """Train age discrimination detection model"""
        if not training_data:
            # Use synthetic training data if none provided
            training_data = self._generate_synthetic_training_data()
        
        # Prepare training data
        texts = [item.text for item in training_data]
        labels = [item.label for item in training_data]
        
        # Feature extraction pipeline
        self.vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 3),
            stop_words='english',
            lowercase=True
        )
        
        # Create feature matrix
        X_tfidf = self.vectorizer.fit_transform(texts)
        
        # Add semantic features if available
        if self.sentence_model:
            X_semantic = self.sentence_model.encode(texts)
            X = np.hstack([X_tfidf.toarray(), X_semantic])
        else:
            X = X_tfidf.toarray()
        
        # Add custom age-related features
        X_custom = self._extract_custom_features(texts)
        X = np.hstack([X, X_custom])
        
        y = np.array(labels)
        
        # Train ensemble model
        self.model = GradientBoostingClassifier(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42
        )
        
        # Split data for validation
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train model
        self.model.fit(X_train, y_train)
        
        # Evaluate model
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        # Cross-validation
        cv_scores = cross_val_score(self.model, X, y, cv=5)
        
        self.is_trained = True
        self.feature_names = (
            list(self.vectorizer.get_feature_names_out()) +
            [f'semantic_{i}' for i in range(384 if self.sentence_model else 0)] +
            ['age_keyword_count', 'age_context_score', 'discriminatory_language_score']
        )
        
        training_results = {
            'train_accuracy': train_score,
            'test_accuracy': test_score,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'training_samples': len(training_data),
            'feature_count': len(self.feature_names)
        }
        
        logging.info(f"Age discrimination model trained: {training_results}")
        return training_results
    
    def predict(self, text: str, context: Dict = None) -> MLPrediction:
        """Predict age discrimination in text"""
        if not self.is_trained:
            # Use fallback pattern matching if model not trained
            return self._fallback_prediction(text, context)
        
        # Extract features
        X_tfidf = self.vectorizer.transform([text])
        
        if self.sentence_model:
            X_semantic = self.sentence_model.encode([text])
            X = np.hstack([X_tfidf.toarray(), X_semantic])
        else:
            X = X_tfidf.toarray()
        
        X_custom = self._extract_custom_features([text])
        X = np.hstack([X, X_custom])
        
        # Make prediction
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        
        # Get class labels
        classes = self.model.classes_
        prob_dict = {classes[i]: probabilities[i] for i in range(len(classes))}
        
        confidence = max(probabilities)
        
        return MLPrediction(
            prediction=prediction,
            confidence=confidence,
            probabilities=prob_dict,
            features_used=self.feature_names[:10],  # Top features
            model_version=self.model_version
        )
    
    def _extract_custom_features(self, texts: List[str]) -> np.ndarray:
        """Extract custom age-related features"""
        features = []
        
        for text in texts:
            text_lower = text.lower()
            
            # Age keyword count
            age_keyword_count = sum(1 for keyword in self.age_keywords if keyword in text_lower)
            
            # Age context score (proximity to employment decisions)
            decision_words = ['dismiss', 'terminate', 'remove', 'replace', 'restructure', 'retire']
            age_context_score = 0
            for age_word in ['age', 'old', 'young', 'retire']:
                if age_word in text_lower:
                    for decision_word in decision_words:
                        if decision_word in text_lower:
                            age_context_score += 1
            
            # Discriminatory language score
            discriminatory_phrases = [
                'too old', 'past it', 'make way', 'fresh blood', 'new blood',
                'behind the times', 'set in ways', 'cant adapt'
            ]
            discriminatory_score = sum(1 for phrase in discriminatory_phrases if phrase in text_lower)
            
            features.append([age_keyword_count, age_context_score, discriminatory_score])
        
        return np.array(features)
    
    def _generate_synthetic_training_data(self) -> List[TrainingData]:
        """Generate synthetic training data for age discrimination"""
        positive_examples = [
            "Wayne is 56 and should consider retirement. He's too old for this role.",
            "We need fresh blood in this department. The older employees are set in their ways.",
            "At your age, you might find it difficult to adapt to new technology.",
            "It's time to make way for younger, more energetic staff members.",
            "The pension costs for older employees are becoming too expensive.",
            "He's past it and can't keep up with modern practices.",
            "We're looking for someone with a more contemporary approach, not old school methods."
        ]
        
        negative_examples = [
            "Wayne has excellent performance reviews and strong technical skills.",
            "His experience and knowledge are valuable assets to the team.",
            "The decision was based on performance metrics, not personal characteristics.",
            "We need to restructure due to budget constraints affecting all departments.",
            "The role requires specific qualifications that weren't met.",
            "This is a standard organizational change affecting multiple positions.",
            "Performance improvement is needed across all age groups."
        ]
        
        training_data = []
        
        for text in positive_examples:
            training_data.append(TrainingData(
                text=text,
                label='age_discrimination',
                metadata={'synthetic': True, 'type': 'positive'}
            ))
        
        for text in negative_examples:
            training_data.append(TrainingData(
                text=text,
                label='no_discrimination',
                metadata={'synthetic': True, 'type': 'negative'}
            ))
        
        return training_data
    
    def _fallback_prediction(self, text: str, context: Dict = None) -> MLPrediction:
        """Fallback prediction using pattern matching"""
        text_lower = text.lower()
        
        # Count age-related keywords
        keyword_matches = sum(1 for keyword in self.age_keywords if keyword in text_lower)
        
        # Calculate confidence based on keyword density and context
        confidence = min(keyword_matches * 0.2, 0.9)
        
        # Determine prediction
        if keyword_matches >= 2:
            prediction = 'age_discrimination'
        else:
            prediction = 'no_discrimination'
        
        probabilities = {
            'age_discrimination': confidence if prediction == 'age_discrimination' else 1 - confidence,
            'no_discrimination': confidence if prediction == 'no_discrimination' else 1 - confidence
        }
        
        return MLPrediction(
            prediction=prediction,
            confidence=confidence,
            probabilities=probabilities,
            features_used=['keyword_matching'],
            model_version='fallback'
        )

class ProceduralFairnessModel(LegalMLModel):
    """ML model for detecting procedural fairness violations"""
    
    def __init__(self):
        super().__init__("procedural_fairness_detector")
        self.violation_indicators = [
            'predetermined', 'biased', 'unfair', 'no investigation', 'already decided',
            'formality', 'going through motions', 'outcome decided', 'waste time',
            'unnecessary procedures', 'speed up', 'get rid of', 'find reason'
        ]
    
    def train(self, training_data: List[TrainingData]) -> Dict[str, Any]:
        """Train procedural fairness model"""
        if not training_data:
            training_data = self._generate_synthetic_training_data()
        
        # Similar implementation to AgeDiscriminationModel
        # Simplified for brevity - would follow same pattern
        
        texts = [item.text for item in training_data]
        labels = [item.label for item in training_data]
        
        self.vectorizer = TfidfVectorizer(max_features=3000, ngram_range=(1, 2))
        X = self.vectorizer.fit_transform(texts)
        
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.model.fit(X, labels)
        
        self.is_trained = True
        return {'status': 'trained', 'samples': len(training_data)}
    
    def predict(self, text: str, context: Dict = None) -> MLPrediction:
        """Predict procedural fairness violations"""
        if not self.is_trained:
            return self._fallback_prediction(text)
        
        X = self.vectorizer.transform([text])
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        
        classes = self.model.classes_
        prob_dict = {classes[i]: probabilities[i] for i in range(len(classes))}
        
        return MLPrediction(
            prediction=prediction,
            confidence=max(probabilities),
            probabilities=prob_dict,
            features_used=['tfidf_features'],
            model_version=self.model_version
        )
    
    def _generate_synthetic_training_data(self) -> List[TrainingData]:
        """Generate synthetic training data for procedural fairness"""
        positive_examples = [
            "The investigation is just a formality - we already know the outcome.",
            "I've already decided we need to get rid of him. Let's speed this up.",
            "No need to interview witnesses, we have enough evidence already.",
            "The decision has been predetermined, this is just going through the motions.",
            "We don't need to follow all these unnecessary procedures."
        ]
        
        negative_examples = [
            "We need to conduct a thorough and fair investigation.",
            "All witnesses will be interviewed as part of the process.",
            "The decision will be made based on the evidence gathered.",
            "We must follow proper procedures to ensure fairness.",
            "The investigation will be conducted impartially."
        ]
        
        training_data = []
        
        for text in positive_examples:
            training_data.append(TrainingData(
                text=text,
                label='procedural_violation',
                metadata={'synthetic': True}
            ))
        
        for text in negative_examples:
            training_data.append(TrainingData(
                text=text,
                label='fair_procedure',
                metadata={'synthetic': True}
            ))
        
        return training_data
    
    def _fallback_prediction(self, text: str) -> MLPrediction:
        """Fallback prediction for procedural fairness"""
        text_lower = text.lower()
        
        violation_count = sum(1 for indicator in self.violation_indicators if indicator in text_lower)
        confidence = min(violation_count * 0.25, 0.9)
        
        prediction = 'procedural_violation' if violation_count >= 1 else 'fair_procedure'
        
        probabilities = {
            'procedural_violation': confidence if prediction == 'procedural_violation' else 1 - confidence,
            'fair_procedure': confidence if prediction == 'fair_procedure' else 1 - confidence
        }
        
        return MLPrediction(
            prediction=prediction,
            confidence=confidence,
            probabilities=probabilities,
            features_used=['keyword_matching'],
            model_version='fallback'
        )

if __name__ == "__main__":
    # Test the ML models
    age_model = AgeDiscriminationModel()
    procedural_model = ProceduralFairnessModel()
    
    # Train with synthetic data
    age_results = age_model.train([])
    procedural_results = procedural_model.train([])
    
    print("ML models initialized and trained with synthetic data")
    print(f"Age discrimination model: {age_results}")
    print(f"Procedural fairness model: {procedural_results}")
    
    # Test predictions
    test_text = "Wayne is 56 and should retire. The investigation is just a formality."
    
    age_pred = age_model.predict(test_text)
    proc_pred = procedural_model.predict(test_text)
    
    print(f"\nAge discrimination prediction: {age_pred.prediction} ({age_pred.confidence:.2f})")
    print(f"Procedural fairness prediction: {proc_pred.prediction} ({proc_pred.confidence:.2f})")
