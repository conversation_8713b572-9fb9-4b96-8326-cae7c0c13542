"""
Analysis Engine for Legal Rebuttal Analysis System
Advanced ML-powered analysis with fallback to pattern matching
"""

import re
import logging
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

# Import ML models
try:
    from ml_models import AgeDiscriminationModel, ProceduralFairnessModel, MLPrediction
    ML_AVAILABLE = True
except ImportError:
    logging.warning("ML models not available, using pattern matching fallback")
    ML_AVAILABLE = False

@dataclass
class AnalysisResult:
    finding_type: str
    description: str
    confidence_score: float
    evidence_text: str
    recommendation: str
    ml_prediction: Optional[Dict] = None
    analysis_method: str = "pattern_matching"

class LegalAnalyzer:
    """Advanced legal analyzer with ML models and pattern matching fallback"""

    def __init__(self, config: Dict):
        self.config = config
        self.age_patterns = config.get('patterns', {}).get('age_discrimination', [])
        self.service_patterns = config.get('patterns', {}).get('long_service_devaluation', [])
        self.procedural_patterns = config.get('patterns', {}).get('procedural_violations', [])
        self.user_age = config.get('user_profile', {}).get('age', 56)
        self.service_years = config.get('user_profile', {}).get('service_years', 30)

        # Initialize ML models if available
        self.ml_models = {}
        if ML_AVAILABLE:
            try:
                self.ml_models['age_discrimination'] = AgeDiscriminationModel()
                self.ml_models['procedural_fairness'] = ProceduralFairnessModel()

                # Try to load pre-trained models or train with synthetic data
                self._initialize_ml_models()

                logging.info("ML models initialized successfully")
            except Exception as e:
                logging.warning(f"Failed to initialize ML models: {e}")
                self.ml_models = {}
        else:
            logging.info("Using pattern matching analysis (ML models not available)")
    
    def _initialize_ml_models(self):
        """Initialize ML models with training data"""
        try:
            import os
            # Try to load pre-trained models
            models_dir = "models"
            os.makedirs(models_dir, exist_ok=True)

            for model_name, model in self.ml_models.items():
                model_path = os.path.join(models_dir, f"{model_name}_model.pkl")
                if os.path.exists(model_path):
                    model.load_model(model_path)
                    logging.info(f"Loaded pre-trained {model_name} model")
                else:
                    # Train with synthetic data
                    logging.info(f"Training {model_name} model with synthetic data")
                    model.train([])  # Empty list triggers synthetic data generation
                    model.save_model(model_path)

        except Exception as e:
            logging.error(f"Error initializing ML models: {e}")

    def analyze_age_discrimination(self, text: str) -> List[AnalysisResult]:
        """Detect age discrimination patterns using ML models with pattern matching fallback"""
        results = []

        # Try ML model first
        if 'age_discrimination' in self.ml_models:
            try:
                ml_prediction = self.ml_models['age_discrimination'].predict(text)

                if ml_prediction.prediction == 'age_discrimination' and ml_prediction.confidence > 0.5:
                    context = self._extract_context(text, "age discrimination", 150)

                    result = AnalysisResult(
                        finding_type="age_discrimination",
                        description=f"ML-detected age discrimination (confidence: {ml_prediction.confidence:.1%})",
                        confidence_score=ml_prediction.confidence,
                        evidence_text=context,
                        recommendation=self._get_ml_age_discrimination_recommendation(ml_prediction),
                        ml_prediction=ml_prediction.__dict__,
                        analysis_method="machine_learning"
                    )
                    results.append(result)

            except Exception as e:
                logging.error(f"ML age discrimination analysis failed: {e}")

        # Fallback to pattern matching (also runs alongside ML for comprehensive analysis)
        pattern_results = self._analyze_age_discrimination_patterns(text)
        results.extend(pattern_results)
        
        return results

    def _analyze_age_discrimination_patterns(self, text: str) -> List[AnalysisResult]:
        """Pattern-based age discrimination analysis (fallback method)"""
        results = []
        text_lower = text.lower()

        for pattern in self.age_patterns:
            if pattern.lower() in text_lower:
                # Find the context around the pattern
                context = self._extract_context(text, pattern, 100)

                confidence = self._calculate_age_discrimination_confidence(text, pattern)

                result = AnalysisResult(
                    finding_type="age_discrimination",
                    description=f"Age discrimination pattern detected: '{pattern}'",
                    confidence_score=confidence,
                    evidence_text=context,
                    recommendation=self._get_age_discrimination_recommendation(pattern),
                    analysis_method="pattern_matching"
                )
                results.append(result)

        # Special check for Wayne's age (56)
        if "56" in text or str(self.user_age) in text:
            context = self._extract_context(text, str(self.user_age), 150)
            result = AnalysisResult(
                finding_type="age_discrimination",
                description=f"Direct reference to Wayne's age ({self.user_age}) - potential age discrimination",
                confidence_score=0.8,
                evidence_text=context,
                recommendation="This direct reference to Wayne's age could constitute age discrimination under the Equality Act 2010. Consider challenging this as evidence of age-related bias.",
                analysis_method="pattern_matching"
            )
            results.append(result)

        return results

    def _get_ml_age_discrimination_recommendation(self, ml_prediction) -> str:
        """Get recommendation based on ML prediction"""
        base_recommendation = "ML analysis detected age discrimination patterns. "

        if ml_prediction.confidence > 0.8:
            return base_recommendation + "High confidence detection suggests strong evidence of age bias. This should be a primary focus of Wayne's defense under the Equality Act 2010."
        elif ml_prediction.confidence > 0.6:
            return base_recommendation + "Moderate confidence suggests potential age discrimination. Consider this as supporting evidence in Wayne's case."
        else:
            return base_recommendation + "Lower confidence detection. Review context carefully and consider as potential supporting evidence."
    
    def analyze_long_service_devaluation(self, text: str) -> List[AnalysisResult]:
        """Detect patterns that devalue long service"""
        results = []
        text_lower = text.lower()
        
        for pattern in self.service_patterns:
            if pattern.lower() in text_lower:
                context = self._extract_context(text, pattern, 100)
                
                confidence = 0.7  # Base confidence for service devaluation
                
                result = AnalysisResult(
                    finding_type="long_service_devaluation",
                    description=f"Long service devaluation pattern: '{pattern}'",
                    confidence_score=confidence,
                    evidence_text=context,
                    recommendation=f"This language suggests targeting Wayne due to his {self.service_years}+ years of service and associated costs. This could constitute indirect age discrimination."
                )
                results.append(result)
        
        return results
    
    def analyze_procedural_violations(self, text: str) -> List[AnalysisResult]:
        """Detect procedural fairness violations"""
        results = []
        text_lower = text.lower()
        
        for pattern in self.procedural_patterns:
            if pattern.lower() in text_lower:
                context = self._extract_context(text, pattern, 100)
                
                confidence = 0.8  # High confidence for procedural violations
                
                result = AnalysisResult(
                    finding_type="procedural_violation",
                    description=f"Procedural violation detected: '{pattern}'",
                    confidence_score=confidence,
                    evidence_text=context,
                    recommendation="This suggests a breach of procedural fairness and natural justice principles. The ACAS Code of Practice requires fair and unbiased investigations."
                )
                results.append(result)
        
        return results
    
    def analyze_critique_mode(self, text: str, file_path: str) -> List[AnalysisResult]:
        """Analyze evidence in critique mode (Evidence Against Me folder)"""
        all_results = []
        
        # Run all analysis types for critique mode
        all_results.extend(self.analyze_age_discrimination(text))
        all_results.extend(self.analyze_long_service_devaluation(text))
        all_results.extend(self.analyze_procedural_violations(text))
        
        # Add file context to results
        for result in all_results:
            result.description = f"[{file_path}] {result.description}"
        
        return all_results
    
    def analyze_support_mode(self, text: str, file_path: str) -> List[AnalysisResult]:
        """Analyze evidence in support mode (References folder)"""
        results = []
        text_lower = text.lower()
        
        # Look for positive indicators
        positive_patterns = [
            "excellent", "outstanding", "commendable", "exemplary", 
            "dedicated", "experienced", "knowledgeable", "mentor",
            "valuable", "asset", "contribution", "achievement"
        ]
        
        for pattern in positive_patterns:
            if pattern.lower() in text_lower:
                context = self._extract_context(text, pattern, 100)
                
                result = AnalysisResult(
                    finding_type="positive_evidence",
                    description=f"[{file_path}] Positive evidence: '{pattern}'",
                    confidence_score=0.7,
                    evidence_text=context,
                    recommendation=f"This positive evidence supports Wayne's character and performance. Use this to counter negative allegations and demonstrate his value to the organization."
                )
                results.append(result)
        
        return results
    
    def _extract_context(self, text: str, pattern: str, context_length: int = 100) -> str:
        """Extract context around a pattern match"""
        pattern_pos = text.lower().find(pattern.lower())
        if pattern_pos == -1:
            return text[:context_length]
        
        start = max(0, pattern_pos - context_length // 2)
        end = min(len(text), pattern_pos + len(pattern) + context_length // 2)
        
        context = text[start:end]
        if start > 0:
            context = "..." + context
        if end < len(text):
            context = context + "..."
        
        return context
    
    def _calculate_age_discrimination_confidence(self, text: str, pattern: str) -> float:
        """Calculate confidence score for age discrimination"""
        base_confidence = 0.6
        
        # Boost confidence for stronger patterns
        strong_patterns = ["too old", "past it", "retire", "retirement", "make way"]
        if pattern.lower() in strong_patterns:
            base_confidence += 0.2
        
        # Boost if combined with Wayne's age
        if str(self.user_age) in text:
            base_confidence += 0.1
        
        # Boost if in context of employment decisions
        decision_words = ["dismiss", "terminate", "remove", "replace", "restructure"]
        for word in decision_words:
            if word in text.lower():
                base_confidence += 0.1
                break
        
        return min(1.0, base_confidence)
    
    def _get_age_discrimination_recommendation(self, pattern: str) -> str:
        """Get specific recommendation for age discrimination pattern"""
        recommendations = {
            "retire": "This language suggests pressure to retire based on age, which is direct age discrimination under the Equality Act 2010.",
            "retirement": "References to retirement in employment decisions may constitute age discrimination.",
            "too old": "This is direct age discrimination language that should be challenged.",
            "past it": "This stereotypical language about older employees is discriminatory.",
            "make way": "This suggests targeting older employees to 'make way' for younger ones - clear age discrimination.",
            "younger": "Preferential treatment of younger employees over older ones is age discrimination.",
            "fresh blood": "This language implies older employees are less valuable - indirect age discrimination.",
            "outdated": "Stereotyping older employees as 'outdated' is age discrimination.",
            "can't adapt": "Assumptions about older employees' ability to adapt are discriminatory stereotypes."
        }
        
        return recommendations.get(pattern.lower(), 
            f"The use of '{pattern}' may constitute age discrimination. Consider challenging this language as evidence of age-related bias.")

if __name__ == "__main__":
    # Test the analyzer
    config = {
        'patterns': {
            'age_discrimination': ['retire', 'too old', 'past it', 'younger'],
            'long_service_devaluation': ['expensive', 'costly'],
            'procedural_violations': ['predetermined', 'biased']
        },
        'user_profile': {'age': 56, 'service_years': 30}
    }
    
    analyzer = LegalAnalyzer(config)
    
    test_text = "Wayne is 56 and should consider retirement. He's too old for this role and we need younger staff."
    
    results = analyzer.analyze_critique_mode(test_text, "test.txt")
    
    for result in results:
        print(f"Finding: {result.finding_type}")
        print(f"Description: {result.description}")
        print(f"Confidence: {result.confidence_score}")
        print(f"Recommendation: {result.recommendation}")
        print("---")
