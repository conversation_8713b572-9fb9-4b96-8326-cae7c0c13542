"""
Synthetic Data Generator for Legal Rebuttal Analysis System
Creates fake evidence files for testing - 24 Hour MVP
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict
import random

class SyntheticDataGenerator:
    def __init__(self, output_dir: str = "test_data"):
        self.output_dir = output_dir
        self.ensure_directories()
    
    def ensure_directories(self):
        """Create test data directories"""
        os.makedirs(f"{self.output_dir}/Evidence Against Me", exist_ok=True)
        os.makedirs(f"{self.output_dir}/References", exist_ok=True)
        os.makedirs(f"{self.output_dir}/Generated Output", exist_ok=True)
    
    def generate_age_discrimination_email(self) -> str:
        """Generate email content with age discrimination patterns"""
        templates = [
            """From: <EMAIL>
To: <EMAIL>
Subject: Performance Discussion
Date: {date}

Wayne,

I think it's time we discussed your future here. At 56, you might want to consider whether you're still the right fit for this role. The younger staff seem to adapt much better to our new digital systems. Perhaps it's time to think about retirement and make way for fresh blood who can bring a more modern perspective to the team.

The technology changes are happening fast, and frankly, some of the older employees are struggling to keep up. We need people who can embrace change, not those who are set in their ways.

Best regards,
Management""",
            
            """From: <EMAIL>
To: <EMAIL>
Subject: Restructuring Discussion
Date: {date}

Wayne,

As part of our modernization efforts, we're looking at streamlining our senior positions. Your salary level and pension contributions are quite expensive for the organization. We're considering bringing in younger talent who would be more cost-effective and better suited to our digital transformation.

At your age, you might find the pace of change challenging. Have you considered early retirement? It might be the best option for everyone.

Regards,
HR Department""",
        ]
        
        template = random.choice(templates)
        date = datetime.now() - timedelta(days=random.randint(30, 365))
        return template.format(date=date.strftime("%Y-%m-%d %H:%M:%S"))
    
    def generate_procedural_violation_email(self) -> str:
        """Generate email showing procedural violations"""
        templates = [
            """From: <EMAIL>
To: <EMAIL>
Subject: Investigation Update
Date: {date}

The investigation is pretty much concluded. We already know what we're going to find - Wayne's clearly not suitable for the role anymore. No need to interview all those witnesses he suggested, we have enough evidence already.

Let's wrap this up quickly. The decision has essentially been made.

Investigator""",
            
            """From: <EMAIL>
To: <EMAIL>
Subject: Wayne Gault Situation
Date: {date}

I've already decided we need to get rid of Wayne. He's too expensive and too set in his ways. The investigation is just a formality - we know what the outcome needs to be.

Can we speed this process up? I don't want to waste time on unnecessary procedures.

Manager"""
        ]
        
        template = random.choice(templates)
        date = datetime.now() - timedelta(days=random.randint(10, 180))
        return template.format(date=date.strftime("%Y-%m-%d %H:%M:%S"))
    
    def generate_supporting_evidence(self) -> str:
        """Generate evidence supporting Wayne's case"""
        templates = [
            """From: <EMAIL>
To: <EMAIL>
Subject: Thank you for your mentoring
Date: {date}

Wayne,

I wanted to thank you for all the guidance you've provided over the years. Your 30+ years of experience have been invaluable to our team. The way you've trained new staff and shared your institutional knowledge has made such a difference.

Your dedication to patient care and your deep understanding of NHS procedures are irreplaceable. I hope management recognizes what an asset you are to the organization.

Best regards,
Grateful Colleague""",
            
            """From: <EMAIL>
To: <EMAIL>
Subject: Commendation for Excellent Service
Date: {date}

Dear Wayne,

I wanted to formally commend you for your outstanding service to our patients. Your experience and dedication over three decades have resulted in consistently excellent patient outcomes.

Your institutional knowledge and mentoring of junior staff have been exemplary. The NHS is fortunate to have someone of your caliber and experience.

Sincerely,
Patient Advocate"""
        ]
        
        template = random.choice(templates)
        date = datetime.now() - timedelta(days=random.randint(1, 90))
        return template.format(date=date.strftime("%Y-%m-%d %H:%M:%S"))
    
    def create_test_files(self):
        """Create complete set of test files"""
        # Evidence Against Me (files to critique)
        evidence_against = [
            ("age_discrimination_email_1.txt", self.generate_age_discrimination_email()),
            ("age_discrimination_email_2.txt", self.generate_age_discrimination_email()),
            ("procedural_violation_1.txt", self.generate_procedural_violation_email()),
            ("procedural_violation_2.txt", self.generate_procedural_violation_email()),
        ]
        
        for filename, content in evidence_against:
            with open(f"{self.output_dir}/Evidence Against Me/{filename}", "w") as f:
                f.write(content)
        
        # References (supporting evidence)
        references = [
            ("positive_feedback_1.txt", self.generate_supporting_evidence()),
            ("positive_feedback_2.txt", self.generate_supporting_evidence()),
            ("commendation_letter.txt", self.generate_supporting_evidence()),
        ]
        
        for filename, content in references:
            with open(f"{self.output_dir}/References/{filename}", "w") as f:
                f.write(content)
        
        # Create a sample rebuttal document
        rebuttal_content = """Wayne Gault's Response to Allegations

1. Timeline of Events
[This section contains chronological events with references to evidence]

2. Response to Allegations
[This section needs enhancement with AI analysis]

3. Supporting Evidence
[This section references files in the References folder]
"""
        
        with open(f"{self.output_dir}/Wayne Gault's Response v1.docx", "w") as f:
            f.write(rebuttal_content)
        
        print(f"✅ Synthetic test data created in {self.output_dir}/")
        print("📁 Evidence Against Me: 4 files with age discrimination and procedural violations")
        print("📁 References: 3 files with supporting evidence")
        print("📄 Sample rebuttal document created")

if __name__ == "__main__":
    generator = SyntheticDataGenerator()
    generator.create_test_files()
