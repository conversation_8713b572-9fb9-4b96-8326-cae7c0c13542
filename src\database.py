"""
Database module for Legal Rebuttal Analysis System
Simple SQLite implementation for 24-hour MVP
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class Evidence:
    id: Optional[int]
    file_path: str
    file_type: str
    content: str
    analysis_mode: str  # 'critique' or 'support'
    created_date: datetime
    
@dataclass
class Finding:
    id: Optional[int]
    evidence_id: int
    finding_type: str  # 'age_discrimination', 'procedural_violation', etc.
    description: str
    confidence_score: float
    created_date: datetime

class Database:
    def __init__(self, db_path: str = "legal_analysis.db"):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """Initialize database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS evidence (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT NOT NULL,
                    file_type TEXT NOT NULL,
                    content TEXT,
                    analysis_mode TEXT NOT NULL,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS findings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    evidence_id INTEGER,
                    finding_type TEXT NOT NULL,
                    description TEXT NOT NULL,
                    confidence_score REAL,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (evidence_id) REFERENCES evidence (id)
                )
            """)
            
            conn.commit()
            logging.info("Database initialized successfully")
    
    def store_evidence(self, evidence: Evidence) -> int:
        """Store evidence file information"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO evidence (file_path, file_type, content, analysis_mode)
                VALUES (?, ?, ?, ?)
            """, (evidence.file_path, evidence.file_type, evidence.content, evidence.analysis_mode))
            return cursor.lastrowid
    
    def store_finding(self, finding: Finding) -> int:
        """Store analysis finding"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                INSERT INTO findings (evidence_id, finding_type, description, confidence_score)
                VALUES (?, ?, ?, ?)
            """, (finding.evidence_id, finding.finding_type, finding.description, finding.confidence_score))
            return cursor.lastrowid
    
    def get_findings_by_type(self, finding_type: str) -> List[Dict]:
        """Get all findings of a specific type"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT f.*, e.file_path, e.analysis_mode
                FROM findings f
                JOIN evidence e ON f.evidence_id = e.id
                WHERE f.finding_type = ?
                ORDER BY f.confidence_score DESC
            """, (finding_type,))
            return [dict(row) for row in cursor.fetchall()]
    
    def get_all_findings(self) -> List[Dict]:
        """Get all findings with evidence information"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT f.*, e.file_path, e.analysis_mode
                FROM findings f
                JOIN evidence e ON f.evidence_id = e.id
                ORDER BY f.confidence_score DESC, f.created_date DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
    
    def clear_all_data(self):
        """Clear all data (for testing)"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("DELETE FROM findings")
            conn.execute("DELETE FROM evidence")
            conn.commit()
            logging.info("Database cleared")

if __name__ == "__main__":
    # Test the database
    db = Database()
    print("Database initialized successfully")
