"""
Enhanced Command Line Interface for Legal Rebuttal Analysis System
Full implementation with all advanced features
"""

import os
import sys
import yaml
import logging
import click
from datetime import datetime
from typing import List, Dict, Optional
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel

# Add src to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import all system components
from database import Database, Evidence, Finding
from file_handlers import FileHandlerFactory, process_file
from analysis_engine import LegalAnalyzer
from document_enhancer import DocumentEnhancer
from synthetic_data import SyntheticDataGenerator

# Import advanced components
try:
    from ml_models import AgeDiscriminationModel, ProceduralFairnessModel
    from legal_knowledge_base import LegalKnowledgeBase
    from professional_document_processor import ProfessionalDocumentProcessor, DocumentEnhancement
    ADVANCED_FEATURES = True
except ImportError as e:
    logging.warning(f"Advanced features not available: {e}")
    ADVANCED_FEATURES = False

# Rich console for enhanced output
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_analysis.log'),
        logging.StreamHandler()
    ]
)

class EnhancedLegalRebuttalSystem:
    """Enhanced system orchestrator with full feature set"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
        self.db = Database(self.config.get('paths', {}).get('database', 'legal_analysis.db'))
        self.analyzer = LegalAnalyzer(self.config)
        self.enhancer = DocumentEnhancer(self.config)
        
        # Initialize advanced components if available
        if ADVANCED_FEATURES:
            self.legal_kb = LegalKnowledgeBase()
            self.professional_processor = ProfessionalDocumentProcessor(self.config)
            console.print("✅ Advanced features initialized", style="green")
        else:
            console.print("⚠️ Running with basic features only", style="yellow")
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            console.print(f"⚠️ Config file not found: {config_path}", style="yellow")
            return self._get_default_config()
        except Exception as e:
            console.print(f"❌ Error loading config: {e}", style="red")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """Get default configuration"""
        return {
            'paths': {
                'evidence_against': 'Evidence Against Me/',
                'references': 'References/',
                'rebuttal_document': "Wayne Gault's Response v1.docx",
                'output_directory': 'Generated Output/',
                'database': 'legal_analysis.db'
            },
            'analysis': {
                'confidence_threshold': 0.6,
                'use_ml_models': ADVANCED_FEATURES
            },
            'user_profile': {
                'age': 56,
                'service_years': 30,
                'employer': 'NHS Scotland'
            }
        }
    
    def run_comprehensive_analysis(self) -> Dict:
        """Run comprehensive analysis with all advanced features"""
        console.print("\n🚀 Starting Comprehensive Legal Analysis", style="bold blue")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # Clear previous analysis
            task1 = progress.add_task("Clearing previous analysis...", total=None)
            self.db.clear_all_data()
            progress.update(task1, completed=True)
            
            # Analyze evidence folders
            task2 = progress.add_task("Analyzing evidence files...", total=None)
            results = self._analyze_all_evidence()
            progress.update(task2, completed=True)
            
            # Generate legal knowledge insights
            if ADVANCED_FEATURES:
                task3 = progress.add_task("Consulting legal knowledge base...", total=None)
                legal_insights = self._get_legal_insights(results)
                results['legal_insights'] = legal_insights
                progress.update(task3, completed=True)
                
                # Calculate remedy estimates
                task4 = progress.add_task("Calculating remedy estimates...", total=None)
                remedy_estimates = self._calculate_remedies(results)
                results['remedy_estimates'] = remedy_estimates
                progress.update(task4, completed=True)
            
            # Generate professional document
            task5 = progress.add_task("Creating enhanced document...", total=None)
            enhanced_doc = self._create_enhanced_document(results)
            results['enhanced_document'] = enhanced_doc
            progress.update(task5, completed=True)
        
        return results
    
    def _analyze_all_evidence(self) -> Dict:
        """Analyze all evidence with enhanced processing"""
        paths = self.config.get('paths', {})
        evidence_against_path = paths.get('evidence_against', 'Evidence Against Me/')
        references_path = paths.get('references', 'References/')
        
        results = {
            'critique_findings': [],
            'support_findings': [],
            'file_processing_stats': {},
            'ml_analysis_used': ADVANCED_FEATURES
        }
        
        # Analyze "Evidence Against Me" folder
        if os.path.exists(evidence_against_path):
            critique_findings, critique_stats = self._analyze_evidence_folder_enhanced(
                evidence_against_path, 'critique'
            )
            results['critique_findings'] = critique_findings
            results['file_processing_stats']['critique'] = critique_stats
        
        # Analyze "References" folder
        if os.path.exists(references_path):
            support_findings, support_stats = self._analyze_evidence_folder_enhanced(
                references_path, 'support'
            )
            results['support_findings'] = support_findings
            results['file_processing_stats']['support'] = support_stats
        
        return results
    
    def _analyze_evidence_folder_enhanced(self, folder_path: str, analysis_mode: str) -> tuple:
        """Enhanced evidence folder analysis with advanced file processing"""
        findings = []
        stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'file_types': {},
            'advanced_processing': 0
        }
        
        supported_extensions = FileHandlerFactory.supported_extensions()
        
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                stats['total_files'] += 1
                stats['file_types'][file_ext] = stats['file_types'].get(file_ext, 0) + 1
                
                if file_ext in supported_extensions:
                    try:
                        # Process file with enhanced handlers
                        file_result = process_file(file_path)
                        
                        if file_result['error']:
                            stats['failed_files'] += 1
                            continue
                        
                        stats['processed_files'] += 1
                        
                        # Check if advanced processing was used
                        if file_ext in ['.xlsx', '.xls', '.png', '.jpg', '.jpeg']:
                            stats['advanced_processing'] += 1
                        
                        # Store evidence
                        evidence = Evidence(
                            id=None,
                            file_path=file_path,
                            file_type=file_ext,
                            content=file_result['content'],
                            analysis_mode=analysis_mode,
                            created_date=datetime.now()
                        )
                        evidence_id = self.db.store_evidence(evidence)
                        
                        # Analyze with enhanced engine
                        if analysis_mode == 'critique':
                            analysis_findings = self.analyzer.analyze_critique_mode(
                                file_result['content'], file_path
                            )
                        else:
                            analysis_findings = self.analyzer.analyze_support_mode(
                                file_result['content'], file_path
                            )
                        
                        # Store findings and convert to dict format
                        for finding in analysis_findings:
                            db_finding = Finding(
                                id=None,
                                evidence_id=evidence_id,
                                finding_type=finding.finding_type,
                                description=finding.description,
                                confidence_score=finding.confidence_score,
                                created_date=datetime.now()
                            )
                            self.db.store_finding(db_finding)
                            
                            findings.append({
                                'finding_type': finding.finding_type,
                                'description': finding.description,
                                'confidence_score': finding.confidence_score,
                                'evidence_text': finding.evidence_text,
                                'recommendation': finding.recommendation,
                                'file_path': file_path,
                                'analysis_method': getattr(finding, 'analysis_method', 'pattern_matching'),
                                'ml_prediction': getattr(finding, 'ml_prediction', None)
                            })
                            
                    except Exception as e:
                        logging.error(f"Error processing {file_path}: {e}")
                        stats['failed_files'] += 1
        
        return findings, stats
    
    def _get_legal_insights(self, results: Dict) -> Dict:
        """Get insights from legal knowledge base"""
        if not ADVANCED_FEATURES:
            return {}
        
        insights = {}
        
        # Get relevant case law
        age_cases = self.legal_kb.search_case_law(['age', 'discrimination'], 'age_discrimination')
        insights['relevant_cases'] = [
            {
                'case_name': case.case_name,
                'citation': case.citation,
                'legal_principle': case.legal_principle,
                'relevance_score': case.relevance_score
            }
            for case in age_cases[:3]  # Top 3 most relevant
        ]
        
        # Get NHS policies
        nhs_policies = self.legal_kb.search_nhs_policies(['dignity', 'equality', 'governance'])
        insights['relevant_policies'] = [
            {
                'policy_name': policy.policy_name,
                'requirement': policy.requirement,
                'breach_consequences': policy.breach_consequences
            }
            for policy in nhs_policies[:3]
        ]
        
        return insights
    
    def _calculate_remedies(self, results: Dict) -> Dict:
        """Calculate potential remedy amounts"""
        if not ADVANCED_FEATURES:
            return {}
        
        # Determine severity based on findings
        age_discrimination_count = len([
            f for f in results['critique_findings'] 
            if f['finding_type'] == 'age_discrimination'
        ])
        
        if age_discrimination_count >= 3:
            severity = "severe"
        elif age_discrimination_count >= 2:
            severity = "standard"
        else:
            severity = "minor"
        
        user_profile = self.config.get('user_profile', {})
        age = user_profile.get('age', 56)
        service_years = user_profile.get('service_years', 30)
        
        return self.legal_kb.calculate_remedy_estimate(
            'age_discrimination', severity, service_years, age
        )
    
    def _create_enhanced_document(self, results: Dict) -> str:
        """Create enhanced document with professional formatting"""
        paths = self.config.get('paths', {})
        rebuttal_path = paths.get('rebuttal_document', "Wayne Gault's Response v1.docx")
        
        if ADVANCED_FEATURES and os.path.exists(rebuttal_path):
            # Create professional document with advanced processor
            enhancements = self._convert_findings_to_enhancements(results)
            citations = self._get_legal_citations(results)
            
            return self.professional_processor.enhance_document_professional(
                rebuttal_path, enhancements, citations
            )
        else:
            # Fallback to basic enhancement
            all_findings = results['critique_findings'] + results['support_findings']
            return self.enhancer.enhance_document(rebuttal_path, all_findings)
    
    def _convert_findings_to_enhancements(self, results: Dict) -> List:
        """Convert findings to document enhancements"""
        enhancements = []
        
        for finding in results['critique_findings'] + results['support_findings']:
            enhancement = DocumentEnhancement(
                enhancement_type=finding['finding_type'],
                content=finding['description'],
                position="auto",
                legal_basis="Equality Act 2010" if 'age' in finding['finding_type'] else "ACAS Code",
                confidence_score=finding['confidence_score'],
                evidence_file=finding['file_path'],
                recommendation=finding['recommendation']
            )
            enhancements.append(enhancement)
        
        return enhancements
    
    def _get_legal_citations(self, results: Dict) -> List:
        """Get legal citations for document"""
        citations = []
        
        if ADVANCED_FEATURES and 'legal_insights' in results:
            for case in results['legal_insights'].get('relevant_cases', []):
                citations.append({
                    'case_name': case['case_name'],
                    'citation': case['citation'],
                    'relevance': case['legal_principle']
                })
        
        return citations

@click.group()
def cli():
    """Enhanced Legal Rebuttal Analysis System - Full Implementation"""
    pass

@cli.command()
@click.option('--output-dir', default='test_data', help='Output directory for test data')
def generate_test_data(output_dir):
    """Generate synthetic test data including advanced file types"""
    console.print("🔧 Generating comprehensive test data...", style="blue")
    
    generator = SyntheticDataGenerator(output_dir)
    generator.create_test_files()
    
    # TODO: Add Excel and image test files for advanced testing
    
    console.print(f"✅ Test data generated in {output_dir}/", style="green")

@cli.command()
@click.option('--config', default='config.yaml', help='Configuration file path')
@click.option('--professional', is_flag=True, help='Use professional document processing')
def analyze(config, professional):
    """Run comprehensive legal analysis with all advanced features"""
    
    console.print(Panel.fit(
        "🏛️ Enhanced Legal Rebuttal Analysis System\n"
        "⚖️ Full Implementation with Advanced Features\n"
        "🎯 Optimized for Wayne Gault (Age 56, 30+ Years NHS)",
        style="bold blue"
    ))
    
    system = EnhancedLegalRebuttalSystem(config)
    
    # Run comprehensive analysis
    results = system.run_comprehensive_analysis()
    
    # Display enhanced results
    console.print("\n📊 COMPREHENSIVE ANALYSIS RESULTS", style="bold green")
    
    # Create results table
    table = Table(title="Analysis Summary")
    table.add_column("Category", style="cyan")
    table.add_column("Count", style="magenta")
    table.add_column("High Confidence", style="green")
    
    critique_count = len(results['critique_findings'])
    support_count = len(results['support_findings'])
    
    critique_high_conf = len([f for f in results['critique_findings'] if f['confidence_score'] >= 0.8])
    support_high_conf = len([f for f in results['support_findings'] if f['confidence_score'] >= 0.8])
    
    table.add_row("Evidence Against Me", str(critique_count), str(critique_high_conf))
    table.add_row("Supporting Evidence", str(support_count), str(support_high_conf))
    table.add_row("Total Findings", str(critique_count + support_count), str(critique_high_conf + support_high_conf))
    
    console.print(table)
    
    # Display remedy estimates if available
    if 'remedy_estimates' in results:
        remedy = results['remedy_estimates']
        console.print(f"\n💰 ESTIMATED REMEDY CALCULATION", style="bold yellow")
        console.print(f"Injury to Feelings: £{remedy['injury_to_feelings']:,}")
        console.print(f"Statutory Redundancy: £{remedy['statutory_redundancy']:,}")
        console.print(f"Notice Pay: £{remedy['notice_pay']:,}")
        console.print(f"TOTAL ESTIMATE: £{remedy['total_estimate']:,}", style="bold green")
        console.print(f"Vento Band: {remedy['vento_band']}")
    
    # Display file processing stats
    if 'file_processing_stats' in results:
        console.print(f"\n📁 FILE PROCESSING STATISTICS", style="bold cyan")
        for mode, stats in results['file_processing_stats'].items():
            console.print(f"{mode.title()}: {stats['processed_files']}/{stats['total_files']} files processed")
            if stats['advanced_processing'] > 0:
                console.print(f"  Advanced processing: {stats['advanced_processing']} files")
    
    console.print(f"\n✅ Enhanced document: {results['enhanced_document']}", style="bold green")

if __name__ == '__main__':
    cli()
